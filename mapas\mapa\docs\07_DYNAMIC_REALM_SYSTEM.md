# 🌍 AURACRON - DYNAMIC REALM SYSTEM
**Versão**: 2.0 - Documento Unificado  
**Data**: Janeiro de 2025  
**Engine**: Unreal Engine 5.6

---

## 🎯 **CONCEITO CENTRAL**

O **Dynamic Realm System** é a inovação principal de AURACRON: um mapa tridimensional que evolui organicamente durante a partida, criando três camadas distintas de combate com mecânicas únicas e interconectadas.

---

## 🏔️ **ESTRUTURA DE CAMADAS**

### **I. PLANÍCIE RADIANTE (Terrestrial Layer)**
**Função**: Campo base acessível com três Trilhos principais

#### **Características Geológicas**
- **Platôs Cristalinos**: Plataformas elevadas com nós de recursos que mudam de elevação
- **Cânions Vivos**: Ravinas que se expandem/contraem baseadas em ações dos jogadores
- **Florestas Respirantes**: Clusters orgânicos que migram pelo mapa
- **Pontes Tectônicas**: Pontes naturais que se formam e desmoronam baseadas no timer
- **Respiradouros Geotermais**: Fornecem mobilidade vertical entre camadas

#### **Objetivos Exclusivos**
- **Guardião Prismal**: Empurra rota e concede controle territorial
- **Torre Prisma**: Aura de dano em área que protege objetivos chave

### **II. FIRMAMENTO ZEPHYR (Celestial Layer)**
**Função**: Plataformas flutuantes a 70m de altura; domínio de visão vertical

#### **Características Celestiais**
- **Arquipélagos Orbitais**: Cadeias de ilhas que orbitam ao redor de pontos centrais
- **Pontes Aurora**: Caminhos de luz que aparecem/desaparecem com ciclos dia/noite
- **Fortalezas Nuvem**: Posições defensivas que derivam pelo mapa
- **Jardins Estelares**: Áreas ricas em recursos com campos de baixa gravidade
- **Fendas do Vazio**: Pontos de teletransporte entre plataformas distantes

#### **Objetivos Exclusivos**
- **Núcleo de Tempestade**: Buff ofensivo que aumenta dano de área
- **Santuários dos Ventos**: Reduz recarga de habilidades de mobilidade

### **III. ABISMO UMBRIO (Abyssal Layer)**
**Função**: Rede de túneis bioluminescentes; foco em furtividade

#### **Características Subterrâneas**
- **Cavernas Bioluminescentes**: Sistemas auto-iluminados que pulsam com energia
- **Rios de Magma**: Correntes que redirecionam baseadas em ações dos jogadores
- **Labirintos Cristalinos**: Estruturas que se reconfiguram a cada fase
- **Templos Antigos**: Zonas de alto risco e alta recompensa
- **Poças Sombrias**: Áreas de escuridão completa que requerem navegação especial

#### **Objetivos Exclusivos**
- **Leviatã Umbrático**: Lifesteal + penetração de armadura
- **Altares da Sombra**: Portais unidirecionais para flanqueamento

---

## 🌊 **FLUXO PRISMAL - O NÚCLEO SERPENTINO**

### **Conceito de Design**
Um rio de energia massivo, similar a uma serpente, que serpenteia através das três camadas, servindo como o principal objetivo e espinha dorsal estratégica do mapa.

#### **Características Físicas**
- **Largura**: Varia de 20-50 unidades, criando pontos de estrangulamento naturais
- **Padrão de Fluxo**: Caminho serpentino que muda a cada 10 minutos
- **Design Visual**: Energia prismática que muda de cor baseada na equipe controladora
- **Força da Corrente**: Velocidade de fluxo variável afeta movimento e habilidades

### **Ilhas Estratégicas no Fluxo Prismal**

#### **Ilhas Nexus (5 total)**
- **Localização**: Posicionadas em curvas chave do Fluxo Prismal
- **Características**: Torre de controle central, posições defensivas em múltiplos níveis
- **Valor Estratégico**: Controle concede habilidades de manipulação do Fluxo

#### **Ilhas Santuário (8 total)**
- **Localização**: Espalhadas ao longo de seções mais calmas do Fluxo
- **Características**: Fontes de cura, escudos temporários, amplificadores de visão
- **Valor Estratégico**: Zonas seguras para reagrupamento e cura

#### **Ilhas Arsenal (6 total)**
- **Localização**: Próximas a pontos de transição entre camadas
- **Características**: Upgrades de armas, potencializadores de habilidades, buffs temporários
- **Valor Estratégico**: Oportunidades de power spike

#### **Ilhas Caos (4 total)**
- **Localização**: Em pontos de interseção do Fluxo
- **Características**: Perigos ambientais, recompensas de alto risco, terreno instável
- **Valor Estratégico**: Itens que mudam o jogo com risco significativo

---

## ⚡ **SISTEMA DE TRILHOS DINÂMICOS**

### **Solar Trilhos**
- **Aparência**: Correntes de energia dourada que fluem através das três camadas
- **Função**: Fornece boost de velocidade de movimento e regeneração de vida
- **Comportamento Dinâmico**: Segue a posição do sol, mais forte ao meio-dia
- **Valor Estratégico**: Controla o ritmo do mapa e permite rotações agressivas
- **Mecânicas Especiais**: Partículas douradas que deixam rastros de luz

### **Axis Trilhos**
- **Aparência**: Canais cinza/prata neutros que conectam pontos de transição
- **Função**: Permite movimento vertical instantâneo entre camadas
- **Comportamento Dinâmico**: Ativa baseado no controle de equipe dos pontos nexus
- **Valor Estratégico**: Crítico para estratégias multi-camadas e ataques surpresa
- **Mecânicas Especiais**: Padrões geométricos prateados, efeitos de distorção gravitacional

### **Lunar Trilhos**
- **Aparência**: Caminhos etéreos azul-branco visíveis apenas à noite
- **Função**: Concede furtividade e visão aprimorada
- **Comportamento Dinâmico**: Fases com ciclos lunares, cria rotas alternativas
- **Valor Estratégico**: Permite manobras de flanqueamento e operações secretas
- **Mecânicas Especiais**: Névoa azul suave, partículas de poeira estelar

---

## 🔗 **CONECTORES VERTICAIS**

### **Tipos de Conectores**
1. **Portais de Ânima** – permanentes nas bases, conexão direta entre camadas
2. **Fendas Fluxo** – rasgam o solo e permitem descida/ascensão temporária
3. **Cipós Astria** – cordas vegetais escaláveis que crescem dinamicamente
4. **Elevadores de Vórtice** – colunas de vento que transportam tropas e heróis
5. **Respiradouros Geotermais** – ativam periodicamente para mobilidade vertical

### **Mecânicas de Transição**
- **Tempo de Transição**: 2-3 segundos entre camadas
- **Vulnerabilidade**: Jogadores são vulneráveis durante transições
- **Cooldown**: 5 segundos entre usos do mesmo conector
- **Capacidade**: Máximo 3 jogadores simultâneos por conector

---

## ⏰ **TIMELINE DA PARTIDA - FASES DE EVOLUÇÃO**

### **FASE 1: DESPERTAR (0-15 minutos) - Acessível**
- **Dispositivos Entry**: Apenas Planície Radiante ativa, outros realms como "preview zones"
- **Dispositivos Mid/High**: Todas as camadas estáveis e acessíveis
- Trilhos a 50% de poder, efeitos visuais adaptados ao hardware
- Fluxo Prismal flui em padrão predeterminado
- Todas as ilhas totalmente emergidas

### **FASE 2: CONVERGÊNCIA (15-25 minutos) - Escalável**
- **Dispositivos Entry**: Transição suave para Firmamento Zephyr
- **Dispositivos Mid**: 2 camadas simultâneas com transições simplificadas
- **Dispositivos High**: Fronteiras entre camadas começam a se confundir
- Trilhos atingem poder baseado na capacidade do dispositivo
- Corrente do Fluxo Prismal se fortalece gradualmente

### **FASE 3: INTENSIFICAÇÃO (25-35 minutos) - Adaptativa**
- **Dispositivos Entry**: Foco em uma camada principal com elementos visuais das outras
- **Dispositivos Mid**: Mudanças moderadas de terreno, efeitos reduzidos
- **Dispositivos High**: Mudanças dramáticas de terreno completas
- Trilhos se intersectam baseado na capacidade de renderização
- Fluxo Prismal com volatilidade adaptada ao hardware

### **FASE 4: RESOLUÇÃO (35+ minutos) - Unificada**
- **Todos os Dispositivos**: Convergência final adaptada à capacidade
- Mapa se contrai de forma proporcional à performance
- Trilhos convergem com efeitos escaláveis
- Surto final do Fluxo Prismal com intensidade adaptativa

---

## 🎯 **IMPACTO ESTRATÉGICO**

### **Early Game (0-15 min)**
- Foco em farming e positioning clássico
- Aprendizado das mecânicas básicas de camada
- Estabelecimento de controle territorial inicial
- Exploração segura dos conectores verticais

### **Mid Game (15-25 min)**
- Decisões de realm criam vantagens posicionais
- Primeira competição séria por objetivos inter-camadas
- Estratégias de rotação entre trilhos se tornam críticas
- Controle do Fluxo Prismal se torna prioritário

### **Late Game (25+ min)**
- Maestria 3D separa players casuais de profissionais
- Combates simultâneos em múltiplas camadas
- Uso avançado de conectores para flanqueamento
- Convergência final força engajamentos decisivos

---

## 🔧 **IMPLEMENTAÇÃO TÉCNICA**

### **Sistema de Transição de Realms**
- **Layer Transition Manager**: Coordena mudanças suaves entre realms
- **Controle de Visibilidade**: Ajuste gradual da visibilidade de cada realm
- **Mapeamento de Realms Ativos**: Rastreamento de quais realms estão ativos
- **Pré-carregamento de Assets**: Carregamento antecipado de recursos necessários

### **Seamless World Streaming**
- **Predictive Loading**: Pré-carregamento baseado em probabilidade de transição
- **Memory Management**: Garbage collection inteligente de assets não utilizados
- **LOD Transitions**: Transições suaves entre níveis de detalhe
- **Audio Occlusion**: Sistema de oclusão de áudio 3D entre camadas

---

## 🎮 **ADAPTAÇÕES POR HARDWARE**

### **Entry Level**
- **Realms Simplificados**: Transições instantâneas, sem efeitos complexos
- **Trilhos Básicos**: Apenas indicadores visuais essenciais
- **Conectores Simples**: Animações básicas de transição
- **Fluxo Reduzido**: Efeitos de partículas mínimos

### **High-End**
- **Realms Completos**: Todas as transições cinematográficas
- **Trilhos Dinâmicos**: Efeitos completos de partículas e distorção
- **Conectores Avançados**: Animações complexas e efeitos visuais
- **Fluxo Prismal**: Sistema completo de partículas e reflexões

---

**O Dynamic Realm System é o coração de AURACRON, criando uma experiência MOBA verdadeiramente tridimensional e evolutiva que se adapta tanto ao hardware quanto ao skill dos jogadores.**
