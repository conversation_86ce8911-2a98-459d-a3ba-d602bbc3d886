#!/usr/bin/env python3
"""
Auracron - Unique Materials Generator
Cria materiais únicos para features geológicas da Planície Radiante usando Unreal Engine 5.6
Integração com AuracronPCGBridge para dynamic realm integration

Este script define e configura materiais únicos para:
- Crystal Plateaus (8 tipos únicos) - Realm Celestial
- Living Canyons (4 tipos únicos) - Realm Terrestrial
- Water Systems (3 tipos únicos) - Realm Aquatic

Compatível com create_planicie_radiante_base.py
Autor: Auracron Development Team
Versão: 1.0
Data: 2024
"""

import sys
import time
import logging
import math
import random
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# Import Unreal Engine Python API
import unreal_engine as ue
import unreal
from unreal import (
    Material, MaterialInstance, MaterialInstanceDynamic,
    MaterialParameterCollection, MaterialFunction,
    DataLayer, WorldPartition
)

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unique_materials.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# ========================================
# CONFIGURAÇÃO BASE DE TERRENO
# ========================================

# Configuração base compatível com create_planicie_radiante_base.py
TERRAIN_BASE_CONFIG = {
    "size_x": 204800,  # 2048 metros
    "size_y": 204800,  # 2048 metros
    "world_partition_grid_size": 25600,  # 256 metros por célula
    "data_layer_prefix": "PlanicieRadiante",
    "realm_integration": True,
    "materials_path": "/Game/PlanicieRadiante/Materials/"
}

# ========================================
# DEFINIÇÕES DE MATERIAIS
# ========================================

class MaterialType(Enum):
    """Tipos de materiais únicos"""
    CRYSTAL_RADIANT = "crystal_radiant"
    CRYSTAL_AZURE = "crystal_azure"
    CRYSTAL_EMERALD = "crystal_emerald"
    CRYSTAL_VIOLET = "crystal_violet"
    CRYSTAL_GOLDEN = "crystal_golden"
    CRYSTAL_SILVER = "crystal_silver"
    CRYSTAL_CRIMSON = "crystal_crimson"
    CRYSTAL_PRISMATIC = "crystal_prismatic"
    CANYON_ORGANIC = "canyon_organic"
    CANYON_PULSING = "canyon_pulsing"
    CANYON_ETHEREAL = "canyon_ethereal"
    CANYON_MYSTICAL = "canyon_mystical"
    WATER_CRYSTAL = "water_crystal"
    WATER_FLOWING = "water_flowing"
    WATER_MYSTICAL = "water_mystical"

@dataclass
class MaterialConfig:
    """Configuração de material único"""
    name: str
    material_type: MaterialType
    base_color: Tuple[float, float, float, float]  # RGBA
    metallic: float
    roughness: float
    emission_color: Tuple[float, float, float, float]  # RGBA
    emission_intensity: float
    normal_intensity: float
    subsurface_color: Tuple[float, float, float, float]  # RGBA
    subsurface_intensity: float
    special_effects: Dict[str, float]
    texture_paths: Dict[str, str]

# ========================================
# CONFIGURAÇÕES DE MATERIAIS CRYSTAL PLATEAUS
# ========================================

CRYSTAL_MATERIALS_CONFIG = [
    MaterialConfig(
        name="M_CrystalPlateau_Radiant",
        material_type=MaterialType.CRYSTAL_RADIANT,
        base_color=(1.0, 0.8, 0.2, 0.7),  # Dourado translúcido
        metallic=0.1,
        roughness=0.05,
        emission_color=(1.0, 0.9, 0.3, 1.0),
        emission_intensity=2.5,
        normal_intensity=1.2,
        subsurface_color=(1.0, 0.7, 0.1, 1.0),
        subsurface_intensity=0.8,
        special_effects={
            "glow_pulse_rate": 0.8,
            "refraction_intensity": 1.5,
            "crystal_facets": 12.0,
            "light_scattering": 0.9
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Radiant_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Radiant_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Radiant_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Radiant_E"
        }
    ),
    MaterialConfig(
        name="M_CrystalPlateau_Azure",
        material_type=MaterialType.CRYSTAL_AZURE,
        base_color=(0.2, 0.6, 1.0, 0.8),  # Azul cristalino
        metallic=0.05,
        roughness=0.02,
        emission_color=(0.1, 0.5, 1.0, 1.0),
        emission_intensity=1.8,
        normal_intensity=1.0,
        subsurface_color=(0.0, 0.4, 0.9, 1.0),
        subsurface_intensity=1.2,
        special_effects={
            "glow_pulse_rate": 1.2,
            "refraction_intensity": 2.0,
            "crystal_facets": 16.0,
            "light_scattering": 1.1,
            "ice_effect": 0.7
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Azure_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Azure_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Azure_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Azure_E"
        }
    ),
    MaterialConfig(
        name="M_CrystalPlateau_Emerald",
        material_type=MaterialType.CRYSTAL_EMERALD,
        base_color=(0.2, 1.0, 0.4, 0.75),  # Verde esmeralda
        metallic=0.08,
        roughness=0.03,
        emission_color=(0.1, 0.8, 0.2, 1.0),
        emission_intensity=2.2,
        normal_intensity=1.1,
        subsurface_color=(0.0, 0.7, 0.2, 1.0),
        subsurface_intensity=1.0,
        special_effects={
            "glow_pulse_rate": 0.6,
            "refraction_intensity": 1.8,
            "crystal_facets": 14.0,
            "light_scattering": 0.8,
            "nature_harmony": 1.0
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Emerald_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Emerald_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Emerald_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Emerald_E"
        }
    ),
    MaterialConfig(
        name="M_CrystalPlateau_Violet",
        material_type=MaterialType.CRYSTAL_VIOLET,
        base_color=(0.8, 0.2, 1.0, 0.8),  # Violeta místico
        metallic=0.12,
        roughness=0.04,
        emission_color=(0.7, 0.1, 0.9, 1.0),
        emission_intensity=2.0,
        normal_intensity=1.3,
        subsurface_color=(0.6, 0.0, 0.8, 1.0),
        subsurface_intensity=1.1,
        special_effects={
            "glow_pulse_rate": 1.0,
            "refraction_intensity": 1.7,
            "crystal_facets": 18.0,
            "light_scattering": 1.0,
            "mystical_aura": 1.2
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Violet_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Violet_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Violet_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Violet_E"
        }
    ),
    MaterialConfig(
        name="M_CrystalPlateau_Golden",
        material_type=MaterialType.CRYSTAL_GOLDEN,
        base_color=(1.0, 0.9, 0.1, 0.6),  # Ouro puro
        metallic=0.3,
        roughness=0.08,
        emission_color=(1.0, 0.8, 0.0, 1.0),
        emission_intensity=3.0,
        normal_intensity=0.9,
        subsurface_color=(0.9, 0.7, 0.0, 1.0),
        subsurface_intensity=0.6,
        special_effects={
            "glow_pulse_rate": 0.4,
            "refraction_intensity": 1.3,
            "crystal_facets": 10.0,
            "light_scattering": 1.2,
            "golden_shimmer": 1.5
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Golden_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Golden_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Golden_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Golden_E"
        }
    ),
    MaterialConfig(
        name="M_CrystalPlateau_Silver",
        material_type=MaterialType.CRYSTAL_SILVER,
        base_color=(0.9, 0.9, 0.9, 0.7),  # Prata cristalina
        metallic=0.25,
        roughness=0.06,
        emission_color=(0.8, 0.8, 1.0, 1.0),
        emission_intensity=1.5,
        normal_intensity=1.4,
        subsurface_color=(0.7, 0.7, 0.9, 1.0),
        subsurface_intensity=0.9,
        special_effects={
            "glow_pulse_rate": 1.5,
            "refraction_intensity": 1.9,
            "crystal_facets": 20.0,
            "light_scattering": 1.3,
            "mirror_reflection": 0.8
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Silver_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Silver_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Silver_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Silver_E"
        }
    ),
    MaterialConfig(
        name="M_CrystalPlateau_Crimson",
        material_type=MaterialType.CRYSTAL_CRIMSON,
        base_color=(1.0, 0.2, 0.2, 0.8),  # Vermelho sangue
        metallic=0.15,
        roughness=0.07,
        emission_color=(1.0, 0.1, 0.1, 1.0),
        emission_intensity=2.8,
        normal_intensity=1.1,
        subsurface_color=(0.8, 0.0, 0.0, 1.0),
        subsurface_intensity=1.3,
        special_effects={
            "glow_pulse_rate": 1.8,
            "refraction_intensity": 1.6,
            "crystal_facets": 15.0,
            "light_scattering": 0.7,
            "fire_essence": 1.1
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Crimson_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Crimson_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Crimson_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Crimson_E"
        }
    ),
    MaterialConfig(
        name="M_CrystalPlateau_Prismatic",
        material_type=MaterialType.CRYSTAL_PRISMATIC,
        base_color=(0.8, 0.8, 0.8, 0.9),  # Prisma multicolorido
        metallic=0.05,
        roughness=0.01,
        emission_color=(1.0, 1.0, 1.0, 1.0),
        emission_intensity=1.2,
        normal_intensity=1.5,
        subsurface_color=(0.5, 0.5, 0.5, 1.0),
        subsurface_intensity=0.7,
        special_effects={
            "glow_pulse_rate": 2.0,
            "refraction_intensity": 2.5,
            "crystal_facets": 24.0,
            "light_scattering": 1.5,
            "rainbow_dispersion": 2.0,
            "chromatic_aberration": 0.5
        },
        texture_paths={
            "diffuse": "/Game/Textures/Crystals/T_Crystal_Prismatic_D",
            "normal": "/Game/Textures/Crystals/T_Crystal_Prismatic_N",
            "roughness": "/Game/Textures/Crystals/T_Crystal_Prismatic_R",
            "emission": "/Game/Textures/Crystals/T_Crystal_Prismatic_E"
        }
    )
]

# ========================================
# CONFIGURAÇÕES DE MATERIAIS LIVING CANYONS
# ========================================

CANYON_MATERIALS_CONFIG = [
    MaterialConfig(
        name="M_LivingCanyon_Organic",
        material_type=MaterialType.CANYON_ORGANIC,
        base_color=(0.4, 0.3, 0.2, 1.0),  # Terra orgânica
        metallic=0.0,
        roughness=0.8,
        emission_color=(0.2, 0.4, 0.1, 1.0),
        emission_intensity=0.3,
        normal_intensity=2.0,
        subsurface_color=(0.3, 0.2, 0.1, 1.0),
        subsurface_intensity=0.5,
        special_effects={
            "breathing_amplitude": 1.0,
            "breathing_rate": 0.5,
            "organic_growth": 0.8,
            "moss_coverage": 0.6,
            "root_networks": 0.7
        },
        texture_paths={
            "diffuse": "/Game/Textures/Canyons/T_Canyon_Organic_D",
            "normal": "/Game/Textures/Canyons/T_Canyon_Organic_N",
            "roughness": "/Game/Textures/Canyons/T_Canyon_Organic_R",
            "displacement": "/Game/Textures/Canyons/T_Canyon_Organic_H"
        }
    ),
    MaterialConfig(
        name="M_LivingCanyon_Pulsing",
        material_type=MaterialType.CANYON_PULSING,
        base_color=(0.5, 0.2, 0.3, 1.0),  # Vermelho pulsante
        metallic=0.1,
        roughness=0.6,
        emission_color=(0.8, 0.2, 0.3, 1.0),
        emission_intensity=1.5,
        normal_intensity=1.8,
        subsurface_color=(0.6, 0.1, 0.2, 1.0),
        subsurface_intensity=1.2,
        special_effects={
            "breathing_amplitude": 1.5,
            "breathing_rate": 0.3,
            "pulse_intensity": 2.0,
            "vein_networks": 1.0,
            "heartbeat_sync": 1.0
        },
        texture_paths={
            "diffuse": "/Game/Textures/Canyons/T_Canyon_Pulsing_D",
            "normal": "/Game/Textures/Canyons/T_Canyon_Pulsing_N",
            "roughness": "/Game/Textures/Canyons/T_Canyon_Pulsing_R",
            "emission": "/Game/Textures/Canyons/T_Canyon_Pulsing_E"
        }
    ),
    MaterialConfig(
        name="M_LivingCanyon_Ethereal",
        material_type=MaterialType.CANYON_ETHEREAL,
        base_color=(0.3, 0.4, 0.6, 0.9),  # Azul etéreo
        metallic=0.05,
        roughness=0.4,
        emission_color=(0.2, 0.5, 0.8, 1.0),
        emission_intensity=1.0,
        normal_intensity=1.2,
        subsurface_color=(0.1, 0.3, 0.5, 1.0),
        subsurface_intensity=1.5,
        special_effects={
            "breathing_amplitude": 0.8,
            "breathing_rate": 0.4,
            "ethereal_mist": 1.2,
            "spirit_wisps": 0.9,
            "dimensional_shift": 0.6
        },
        texture_paths={
            "diffuse": "/Game/Textures/Canyons/T_Canyon_Ethereal_D",
            "normal": "/Game/Textures/Canyons/T_Canyon_Ethereal_N",
            "roughness": "/Game/Textures/Canyons/T_Canyon_Ethereal_R",
            "opacity": "/Game/Textures/Canyons/T_Canyon_Ethereal_O"
        }
    ),
    MaterialConfig(
        name="M_LivingCanyon_Mystical",
        material_type=MaterialType.CANYON_MYSTICAL,
        base_color=(0.4, 0.2, 0.5, 1.0),  # Roxo místico
        metallic=0.2,
        roughness=0.5,
        emission_color=(0.6, 0.3, 0.8, 1.0),
        emission_intensity=1.8,
        normal_intensity=1.6,
        subsurface_color=(0.3, 0.1, 0.4, 1.0),
        subsurface_intensity=1.0,
        special_effects={
            "breathing_amplitude": 1.2,
            "breathing_rate": 0.6,
            "mystical_runes": 1.0,
            "energy_conduits": 0.8,
            "arcane_resonance": 1.3
        },
        texture_paths={
            "diffuse": "/Game/Textures/Canyons/T_Canyon_Mystical_D",
            "normal": "/Game/Textures/Canyons/T_Canyon_Mystical_N",
            "roughness": "/Game/Textures/Canyons/T_Canyon_Mystical_R",
            "emission": "/Game/Textures/Canyons/T_Canyon_Mystical_E"
        }
    )
]

# ========================================
# CONFIGURAÇÕES DE MATERIAIS WATER SYSTEMS
# ========================================

WATER_MATERIALS_CONFIG = [
    MaterialConfig(
        name="M_WaterSystem_Crystal",
        material_type=MaterialType.WATER_CRYSTAL,
        base_color=(0.8, 0.9, 1.0, 0.3),  # Água cristalina
        metallic=0.0,
        roughness=0.1,
        emission_color=(0.7, 0.8, 1.0, 1.0),
        emission_intensity=0.5,
        normal_intensity=1.0,
        subsurface_color=(0.6, 0.7, 0.9, 1.0),
        subsurface_intensity=2.0,
        special_effects={
            "flow_speed": 1.0,
            "wave_amplitude": 0.5,
            "crystal_clarity": 2.0,
            "refraction_strength": 1.5,
            "foam_intensity": 0.3
        },
        texture_paths={
            "normal": "/Game/Textures/Water/T_Water_Crystal_N",
            "flow_map": "/Game/Textures/Water/T_Water_Crystal_Flow",
            "foam": "/Game/Textures/Water/T_Water_Foam",
            "caustics": "/Game/Textures/Water/T_Water_Caustics"
        }
    ),
    MaterialConfig(
        name="M_WaterSystem_Flowing",
        material_type=MaterialType.WATER_FLOWING,
        base_color=(0.4, 0.6, 0.8, 0.4),  # Água em movimento
        metallic=0.0,
        roughness=0.2,
        emission_color=(0.3, 0.5, 0.7, 1.0),
        emission_intensity=0.3,
        normal_intensity=1.5,
        subsurface_color=(0.2, 0.4, 0.6, 1.0),
        subsurface_intensity=1.8,
        special_effects={
            "flow_speed": 2.0,
            "wave_amplitude": 1.0,
            "turbulence": 0.8,
            "refraction_strength": 1.2,
            "foam_intensity": 0.7,
            "erosion_effect": 0.5
        },
        texture_paths={
            "normal": "/Game/Textures/Water/T_Water_Flowing_N",
            "flow_map": "/Game/Textures/Water/T_Water_Flowing_Flow",
            "foam": "/Game/Textures/Water/T_Water_Foam_Heavy",
            "displacement": "/Game/Textures/Water/T_Water_Displacement"
        }
    ),
    MaterialConfig(
        name="M_WaterSystem_Mystical",
        material_type=MaterialType.WATER_MYSTICAL,
        base_color=(0.5, 0.3, 0.8, 0.5),  # Água mística
        metallic=0.1,
        roughness=0.05,
        emission_color=(0.6, 0.4, 0.9, 1.0),
        emission_intensity=1.2,
        normal_intensity=0.8,
        subsurface_color=(0.4, 0.2, 0.7, 1.0),
        subsurface_intensity=2.5,
        special_effects={
            "flow_speed": 0.5,
            "wave_amplitude": 0.3,
            "mystical_glow": 1.5,
            "refraction_strength": 2.0,
            "foam_intensity": 0.2,
            "energy_ripples": 1.0,
            "dimensional_distortion": 0.4
        },
        texture_paths={
            "normal": "/Game/Textures/Water/T_Water_Mystical_N",
            "flow_map": "/Game/Textures/Water/T_Water_Mystical_Flow",
            "emission": "/Game/Textures/Water/T_Water_Mystical_E",
            "distortion": "/Game/Textures/Water/T_Water_Distortion"
        }
    )
]

# ========================================
# GERADOR DE MATERIAIS ÚNICOS
# ========================================

class UniqueMaterialsGenerator:
    """Gerador de materiais únicos para features geológicas"""
    
    def __init__(self):
        self.created_materials = []
        self.material_instances = {}
        self.world = None
        
        # Integração com sistemas de realms
        self.realms_bridge = None
        self.dynamic_realm_subsystem = None
        self.celestial_realm_id = None  # Para Crystal Plateaus
        self.terrestrial_realm_id = None  # Para Living Canyons
        self.aquatic_realm_id = None  # Para Water Systems
        
        # Estatísticas de performance
        self.performance_stats = {
            "materials_created": 0,
            "creation_time": 0.0,
            "memory_usage": 0.0
        }
        
    def initialize(self) -> bool:
        """Inicializa o sistema de materiais"""
        logger.info("Inicializando gerador de materiais únicos...")
        
        try:
            # Verificar se o sistema de materiais está disponível
            self.world = unreal.EditorLevelLibrary.get_editor_world()
            if not self.world:
                logger.error("Mundo do editor não disponível")
                return False
            
            # Inicializar integração com sistemas de realms
            self.initialize_dynamic_realm_integration()
            self.initialize_auracron_realms_bridge()
                
            logger.info("Sistema de materiais inicializado com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro durante inicialização: {e}")
            return False
    
    def initialize_dynamic_realm_integration(self) -> bool:
        """Inicializa integração com AuracronDynamicRealmSubsystem"""
        try:
            logger.info("[INFO] Inicializando integração com AuracronDynamicRealmSubsystem...")
            
            # Obter o subsistema do mundo atual
            if not self.world:
                logger.error("[ERROR] Mundo do editor não encontrado")
                return False
            
            # Tentar obter o subsistema AuracronDynamicRealmSubsystem usando APIs corretas UE 5.6
            try:
                subsystem_class = unreal.find_class('UAuracronDynamicRealmSubsystem')
                if subsystem_class:
                    # Tentar como world subsystem primeiro
                    self.dynamic_realm_subsystem = self.world.get_subsystem(subsystem_class)
                    if self.dynamic_realm_subsystem:
                        logger.info("[PASS] AuracronDynamicRealmSubsystem encontrado (World Subsystem)")
                        return True
                    else:
                        # Tentar como editor subsystem
                        try:
                            self.dynamic_realm_subsystem = unreal.get_editor_subsystem(subsystem_class)
                            if self.dynamic_realm_subsystem:
                                logger.info("[PASS] AuracronDynamicRealmSubsystem encontrado (Editor Subsystem)")
                                return True
                            else:
                                logger.warning("[WARNING] AuracronDynamicRealmSubsystem não encontrado - continuando sem integração")
                                return False
                        except Exception:
                            logger.warning("[WARNING] AuracronDynamicRealmSubsystem não encontrado - continuando sem integração")
                            return False
                else:
                    logger.warning("[WARNING] Classe AuracronDynamicRealmSubsystem não encontrada - continuando sem integração")
                    return False
            except Exception as e:
                logger.warning(f"[WARNING] Erro ao acessar AuracronDynamicRealmSubsystem: {str(e)}")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Erro na inicialização da integração: {str(e)}")
            return False
    
    def initialize_auracron_realms_bridge(self) -> bool:
        """Inicializa integração com AuracronRealmsBridge para gerenciamento de materiais por realm"""
        try:
            logger.info("[INFO] Inicializando AuracronRealmsBridge para materiais...")
            
            # Obter instância do AuracronRealmsBridge
            self.realms_bridge = unreal.AuracronRealmsBridge.get_instance()
            if not self.realms_bridge:
                logger.warning("[WARNING] AuracronRealmsBridge não encontrado")
                return False
            
            # Verificar se o bridge está inicializado
            if not self.realms_bridge.is_initialized():
                logger.info("[INFO] AuracronRealmsBridge já deve estar inicializado pelo script base")
                return False
            
            # Obter IDs dos realms registrados
            self._get_registered_realm_ids()
            
            return True
                
        except Exception as e:
            logger.error(f"[ERROR] Erro ao inicializar AuracronRealmsBridge: {e}")
            return False
    
    def _get_registered_realm_ids(self) -> bool:
        """Obtém os IDs dos realms já registrados pelo script base"""
        try:
            # Buscar realm Celestial para Crystal Plateaus
            self.celestial_realm_id = self.realms_bridge.find_realm_by_name("PlanicieRadiante_Celestial")
            if self.celestial_realm_id:
                logger.info(f"[SUCCESS] Realm Celestial encontrado: {self.celestial_realm_id}")
            
            # Buscar realm Terrestrial para Living Canyons
            self.terrestrial_realm_id = self.realms_bridge.find_realm_by_name("PlanicieRadiante_Terrestrial_Canyons")
            if self.terrestrial_realm_id:
                logger.info(f"[SUCCESS] Realm Terrestrial encontrado: {self.terrestrial_realm_id}")
            
            # Buscar realm Aquatic para Water Systems
            self.aquatic_realm_id = self.realms_bridge.find_realm_by_name("PlanicieRadiante_Aquatic")
            if self.aquatic_realm_id:
                logger.info(f"[SUCCESS] Realm Aquatic encontrado: {self.aquatic_realm_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Erro ao obter IDs dos realms: {e}")
            return False
    
    def create_all_materials(self) -> bool:
        """Cria todos os materiais únicos"""
        logger.info("Criando todos os materiais únicos...")
        
        try:
            # Criar materiais de crystal plateaus
            if not self._create_crystal_materials():
                return False
                
            # Criar materiais de living canyons
            if not self._create_canyon_materials():
                return False
                
            # Criar materiais de water systems
            if not self._create_water_materials():
                return False
                
            logger.info(f"Todos os materiais criados com sucesso: {len(self.created_materials)} materiais")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao criar materiais: {e}")
            return False
    
    def _create_crystal_materials(self) -> bool:
        """Cria materiais únicos para crystal plateaus"""
        logger.info("Criando materiais de Crystal Plateaus...")
        
        for config in CRYSTAL_MATERIALS_CONFIG:
            try:
                material = self._create_material_from_config(config)
                if material:
                    self.created_materials.append(material)
                    self.material_instances[config.name] = material
                    logger.info(f"  ✓ Material criado: {config.name}")
                else:
                    logger.warning(f"  ✗ Falha ao criar material: {config.name}")
                    
            except Exception as e:
                logger.error(f"  ✗ Erro ao criar material {config.name}: {e}")
                
        return True
    
    def _create_canyon_materials(self) -> bool:
        """Cria materiais únicos para living canyons"""
        logger.info("Criando materiais de Living Canyons...")
        
        for config in CANYON_MATERIALS_CONFIG:
            try:
                material = self._create_material_from_config(config)
                if material:
                    self.created_materials.append(material)
                    self.material_instances[config.name] = material
                    logger.info(f"  ✓ Material criado: {config.name}")
                else:
                    logger.warning(f"  ✗ Falha ao criar material: {config.name}")
                    
            except Exception as e:
                logger.error(f"  ✗ Erro ao criar material {config.name}: {e}")
                
        return True
    
    def _create_water_materials(self) -> bool:
        """Cria materiais únicos para water systems"""
        logger.info("Criando materiais de Water Systems...")
        
        for config in WATER_MATERIALS_CONFIG:
            try:
                material = self._create_material_from_config(config)
                if material:
                    self.created_materials.append(material)
                    self.material_instances[config.name] = material
                    logger.info(f"  ✓ Material criado: {config.name}")
                else:
                    logger.warning(f"  ✗ Falha ao criar material: {config.name}")
                    
            except Exception as e:
                logger.error(f"  ✗ Erro ao criar material {config.name}: {e}")
                
        return True
    
    def _create_material_from_config(self, config: MaterialConfig):
        """Cria um material baseado na configuração"""
        try:
            # Criar material base
            material_path = f"/Game/Materials/{config.name}"
            
            # Criar instância dinâmica do material
            base_material = self._get_base_material_template(config.material_type)
            if not base_material:
                logger.warning(f"Template base não encontrado para {config.material_type}")
                return None
                
            material_instance = unreal.MaterialInstanceDynamic.create(base_material, None)
            
            # Configurar propriedades básicas
            material_instance.set_vector_parameter_value("BaseColor", unreal.LinearColor(*config.base_color))
            material_instance.set_scalar_parameter_value("Metallic", config.metallic)
            material_instance.set_scalar_parameter_value("Roughness", config.roughness)
            material_instance.set_vector_parameter_value("EmissionColor", unreal.LinearColor(*config.emission_color))
            material_instance.set_scalar_parameter_value("EmissionIntensity", config.emission_intensity)
            material_instance.set_scalar_parameter_value("NormalIntensity", config.normal_intensity)
            material_instance.set_vector_parameter_value("SubsurfaceColor", unreal.LinearColor(*config.subsurface_color))
            material_instance.set_scalar_parameter_value("SubsurfaceIntensity", config.subsurface_intensity)
            
            # Configurar efeitos especiais
            for effect_name, effect_value in config.special_effects.items():
                material_instance.set_scalar_parameter_value(effect_name, effect_value)
            
            # Carregar e aplicar texturas
            self._apply_textures_to_material(material_instance, config.texture_paths)
            
            return material_instance
            
        except Exception as e:
            logger.error(f"Erro ao criar material {config.name}: {e}")
            return None
    
    def _get_base_material_template(self, material_type: MaterialType):
        """Obtém o template base para o tipo de material"""
        template_map = {
            # Crystal materials
            MaterialType.CRYSTAL_RADIANT: "/Game/Materials/Templates/M_Crystal_Base",
            MaterialType.CRYSTAL_AZURE: "/Game/Materials/Templates/M_Crystal_Base",
            MaterialType.CRYSTAL_EMERALD: "/Game/Materials/Templates/M_Crystal_Base",
            MaterialType.CRYSTAL_VIOLET: "/Game/Materials/Templates/M_Crystal_Base",
            MaterialType.CRYSTAL_GOLDEN: "/Game/Materials/Templates/M_Crystal_Base",
            MaterialType.CRYSTAL_SILVER: "/Game/Materials/Templates/M_Crystal_Base",
            MaterialType.CRYSTAL_CRIMSON: "/Game/Materials/Templates/M_Crystal_Base",
            MaterialType.CRYSTAL_PRISMATIC: "/Game/Materials/Templates/M_Crystal_Base",
            
            # Canyon materials
            MaterialType.CANYON_ORGANIC: "/Game/Materials/Templates/M_Canyon_Base",
            MaterialType.CANYON_PULSING: "/Game/Materials/Templates/M_Canyon_Base",
            MaterialType.CANYON_ETHEREAL: "/Game/Materials/Templates/M_Canyon_Base",
            MaterialType.CANYON_MYSTICAL: "/Game/Materials/Templates/M_Canyon_Base",
            
            # Water materials
            MaterialType.WATER_CRYSTAL: "/Game/Materials/Templates/M_Water_Base",
            MaterialType.WATER_FLOWING: "/Game/Materials/Templates/M_Water_Base",
            MaterialType.WATER_MYSTICAL: "/Game/Materials/Templates/M_Water_Base"
        }
        
        template_path = template_map.get(material_type)
        if template_path:
            return unreal.EditorAssetLibrary.load_asset(template_path)
        return None
    
    def _apply_textures_to_material(self, material_instance, texture_paths: Dict[str, str]):
        """Aplica texturas ao material"""
        for texture_type, texture_path in texture_paths.items():
            try:
                texture = unreal.EditorAssetLibrary.load_asset(texture_path)
                if texture:
                    material_instance.set_texture_parameter_value(f"{texture_type}Texture", texture)
                else:
                    logger.warning(f"Textura não encontrada: {texture_path}")
            except Exception as e:
                logger.warning(f"Erro ao carregar textura {texture_path}: {e}")
    
    def get_material_instance(self, material_name: str):
        """Obtém instância de material por nome"""
        return self.material_instances.get(material_name)
    
    def validate_materials(self) -> bool:
        """Valida se todos os materiais foram criados corretamente"""
        logger.info("Validando materiais criados...")
        
        expected_count = len(CRYSTAL_MATERIALS_CONFIG) + len(CANYON_MATERIALS_CONFIG) + len(WATER_MATERIALS_CONFIG)
        actual_count = len(self.created_materials)
        
        if actual_count == expected_count:
            logger.info(f"  ✓ Todos os {expected_count} materiais foram criados com sucesso")
            return True
        else:
            logger.warning(f"  ✗ Esperado: {expected_count} materiais, Criado: {actual_count} materiais")
            return False

# ========================================
# FUNÇÃO PRINCIPAL
# ========================================

def main():
    """Função principal para criar materiais únicos"""
    logger.info("=" * 60)
    logger.info("AURACRON - UNIQUE MATERIALS GENERATOR")
    logger.info("=" * 60)
    
    start_time = time.time()
    
    try:
        # Inicializar gerador
        generator = UniqueMaterialsGenerator()
        
        if not generator.initialize():
            logger.error("Falha na inicialização do gerador de materiais")
            return False
        
        # Criar todos os materiais
        if not generator.create_all_materials():
            logger.error("Falha na criação dos materiais")
            return False
        
        # Validar materiais
        if not generator.validate_materials():
            logger.warning("Alguns materiais podem não ter sido criados corretamente")
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("RELATÓRIO FINAL - MATERIAIS ÚNICOS")
        logger.info("=" * 60)
        logger.info(f"Materiais Crystal Plateaus: {len(CRYSTAL_MATERIALS_CONFIG)}")
        logger.info(f"Materiais Living Canyons: {len(CANYON_MATERIALS_CONFIG)}")
        logger.info(f"Materiais Water Systems: {len(WATER_MATERIALS_CONFIG)}")
        logger.info(f"Total de materiais criados: {len(generator.created_materials)}")
        logger.info(f"Tempo de geração: {generation_time:.2f} segundos")
        logger.info("Status: SUCESSO ✓")
        
        return True
        
    except Exception as e:
        logger.error(f"Erro durante execução: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)