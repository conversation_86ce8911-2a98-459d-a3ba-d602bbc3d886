#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar curves necessários para o sistema de VerticalConnectors
Executa dentro do Unreal Engine 5.6
"""

import unreal

def create_vertical_connector_curves():
    """Cria os curves necessários para o sistema de VerticalConnectors"""
    try:
        print("[INFO] Criando curves para VerticalConnectors...")

        # Definir os curves necessários com suas configurações
        curves_to_create = [
            {
                'name': 'Curve_PortalAnima_Movement',
                'path': '/Game/Curves/VerticalConnectors/Curve_PortalAnima_Movement',
                'description': 'Curva de movimento para Portal Anima - movimento suave e místico',
                'keys': [(0.0, 0.0), (0.3, 0.8), (0.7, 0.9), (1.0, 1.0)]  # Aceleração suave
            },
            {
                'name': '<PERSON>urve_FendaFluxo_Movement',
                'path': '/Game/Curves/VerticalConnectors/Curve_FendaFluxo_Movement',
                'description': 'Curva de movimento para Fenda Fluxo - movimento rápido e direto',
                'keys': [(0.0, 0.0), (0.2, 0.6), (0.8, 0.95), (1.0, 1.0)]  # Aceleração rápida
            },
            {
                'name': 'Curve_CipoAstria_Movement',
                'path': '/Game/Curves/VerticalConnectors/Curve_CipoAstria_Movement',
                'description': 'Curva de movimento para Cipo Astria - movimento orgânico e natural',
                'keys': [(0.0, 0.0), (0.25, 0.4), (0.5, 0.7), (0.75, 0.85), (1.0, 1.0)]  # Movimento orgânico
            },
            {
                'name': 'Curve_ElevadorVortice_Movement',
                'path': '/Game/Curves/VerticalConnectors/Curve_ElevadorVortice_Movement',
                'description': 'Curva de movimento para Elevador Vortice - movimento em espiral',
                'keys': [(0.0, 0.0), (0.1, 0.3), (0.4, 0.6), (0.9, 0.9), (1.0, 1.0)]  # Movimento em vórtice
            },
            {
                'name': 'Curve_RespiradoroGeotermal_Movement',
                'path': '/Game/Curves/VerticalConnectors/Curve_RespiradoroGeotermal_Movement',
                'description': 'Curva de movimento para Respiradoro Geotermal - movimento pulsante',
                'keys': [(0.0, 0.0), (0.15, 0.5), (0.3, 0.3), (0.6, 0.8), (0.85, 0.6), (1.0, 1.0)]  # Movimento pulsante
            }
        ]

        curves_created = 0

        # Criar diretório se não existir
        directory_path = '/Game/Curves/VerticalConnectors'
        if not unreal.EditorAssetLibrary.does_directory_exist(directory_path):
            unreal.EditorAssetLibrary.make_directory(directory_path)
            print(f"[INFO] Diretório criado: {directory_path}")

        for curve_config in curves_to_create:
            try:
                # Verificar se o curve já existe
                existing_curve = unreal.EditorAssetLibrary.does_asset_exist(curve_config['path'])
                if existing_curve:
                    print(f"[INFO] Curve já existe: {curve_config['name']}")
                    curves_created += 1
                    continue

                # Criar novo CurveFloat
                curve_factory = unreal.CurveFloatFactory()

                # Criar o asset
                curve_asset = unreal.AssetToolsHelpers.get_asset_tools().create_asset(
                    curve_config['name'],
                    '/Game/Curves/VerticalConnectors',
                    unreal.CurveFloat,
                    curve_factory
                )

                if curve_asset:
                    # Configurar as keys da curva usando método UE 5.6
                    try:
                        # Limpar keys existentes
                        curve_asset.float_curve.reset()
                        
                        # Adicionar keys uma por uma
                        for time, value in curve_config['keys']:
                            curve_asset.float_curve.add_key(time, value)
                        
                        # Configurar interpolação para todas as keys
                        for i in range(len(curve_config['keys'])):
                            key_handle = curve_asset.float_curve.get_key_handle(i)
                            if key_handle.is_valid():
                                curve_asset.float_curve.set_key_interpolation(key_handle, unreal.RichCurveInterpMode.RCIM_CUBIC)
                                curve_asset.float_curve.set_key_tangent_mode(key_handle, unreal.RichCurveTangentMode.RCTM_AUTO)
                        
                        print(f"[SUCCESS] Curve criado: {curve_config['name']}")
                        curves_created += 1
                        
                    except Exception as key_error:
                        print(f"[WARNING] Erro ao configurar keys para {curve_config['name']}: {key_error}")
                        # Tentar método mais simples
                        try:
                            for time, value in curve_config['keys']:
                                curve_asset.float_curve.add_key(time, value)
                            print(f"[SUCCESS] Curve criado com método simples: {curve_config['name']}")
                            curves_created += 1
                        except Exception as alt_error:
                            print(f"[ERROR] Falha ao criar curve {curve_config['name']}: {alt_error}")

                    # Salvar o asset
                    try:
                        unreal.EditorAssetLibrary.save_asset(curve_config['path'])
                    except Exception as save_error:
                        print(f"[WARNING] Erro ao salvar {curve_config['name']}: {save_error}")
                else:
                    print(f"[ERROR] Falha ao criar asset para {curve_config['name']}")

            except Exception as curve_error:
                print(f"[ERROR] Erro ao criar curve {curve_config['name']}: {curve_error}")

        # Resultado final
        total_curves = len(curves_to_create)
        if curves_created == total_curves:
            print(f"[SUCCESS] Todos os {total_curves} curves foram criados/verificados")
            return True
        elif curves_created > 0:
            print(f"[PARTIAL] {curves_created}/{total_curves} curves criados com sucesso")
            return True  # Parcialmente bem-sucedido
        else:
            print("[ERROR] Nenhum curve foi criado")
            return False

    except Exception as e:
        print(f"[ERROR] Erro ao criar curves para VerticalConnectors: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== CRIAÇÃO DE CURVES PARA VERTICAL CONNECTORS ===")
    success = create_vertical_connector_curves()
    if success:
        print("✅ Curves criados com sucesso!")
    else:
        print("❌ Falha na criação dos curves")
