// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Realms Dinâmicos Bridge Module Implementation

#include "AuracronRealmsBridgeModule.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"

// Logs
DEFINE_LOG_CATEGORY(LogAuracronRealmsBridge);

#define LOCTEXT_NAMESPACE "FAuracronRealmsBridgeModule"

void FAuracronRealmsBridgeModule::StartupModule()
{
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("AuracronRealmsBridge module starting up..."));
    
    // Inicializar sistemas de realms
    InitializeRealmSystems();
    
    // Registrar integrações com outros módulos
    RegisterModuleIntegrations();
    
    bModuleInitialized = true;
    
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("AuracronRealmsBridge module started successfully"));
}

void FAuracronRealmsBridgeModule::ShutdownModule()
{
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("AuracronRealmsBridge module shutting down..."));
    
    // Limpar sistemas
    CleanupRealmSystems();
    
    bModuleInitialized = false;
    
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("AuracronRealmsBridge module shutdown complete"));
}

bool FAuracronRealmsBridgeModule::IsModuleLoaded()
{
    return FModuleManager::Get().IsModuleLoaded("AuracronRealmsBridge");
}

FAuracronRealmsBridgeModule& FAuracronRealmsBridgeModule::Get()
{
    return FModuleManager::LoadModuleChecked<FAuracronRealmsBridgeModule>("AuracronRealmsBridge");
}

void FAuracronRealmsBridgeModule::InitializeRealmSystems()
{
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Initializing realm systems..."));
    
    // Verificar se os módulos dependentes estão disponíveis
    TArray<FString> RequiredModules = {
        TEXT("Engine"),
        TEXT("CoreUObject"),
        TEXT("ModularGameplay"),
        TEXT("PCG"),
        TEXT("PythonScriptPlugin")
    };
    
    for (const FString& ModuleName : RequiredModules)
    {
        if (FModuleManager::Get().IsModuleLoaded(*ModuleName))
        {
            UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Required module '%s': OK"), *ModuleName);
        }
        else
        {
            UE_LOG(LogAuracronRealmsBridge, Warning, TEXT("Required module '%s': NOT LOADED"), *ModuleName);
        }
    }
    
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Realm systems initialization complete"));
}

void FAuracronRealmsBridgeModule::RegisterModuleIntegrations()
{
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Registering module integrations..."));
    
    // Verificar integrações com outros bridges Auracron
    TArray<FString> IntegrationModules = {
        TEXT("AuracronDynamicRealmBridge"),
        TEXT("AuracronWorldPartitionBridge"),
        TEXT("AuracronPCGBridge"),
        TEXT("AuracronCombatBridge")
    };
    
    for (const FString& ModuleName : IntegrationModules)
    {
        if (FModuleManager::Get().IsModuleLoaded(*ModuleName))
        {
            UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Integration with '%s': OK"), *ModuleName);
        }
        else
        {
            UE_LOG(LogAuracronRealmsBridge, VeryVerbose, TEXT("Integration with '%s': Module not loaded (optional)"), *ModuleName);
        }
    }
    
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Module integrations registered"));
}

void FAuracronRealmsBridgeModule::CleanupRealmSystems()
{
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Cleaning up realm systems..."));
    
    // Limpar qualquer sistema específico se necessário
    // Por enquanto, apenas log
    
    UE_LOG(LogAuracronRealmsBridge, Log, TEXT("Realm systems cleanup complete"));
}

#undef LOCTEXT_NAMESPACE

// Implementar a macro do módulo
IMPLEMENT_MODULE(FAuracronRealmsBridgeModule, AuracronRealmsBridge)
