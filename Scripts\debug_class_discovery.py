#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de debug para descobrir como as classes Auracron estão expostas
"""

import unreal

def debug_class_discovery():
    """Debug detalhado da descoberta de classes"""
    print("=== DEBUG DE DESCOBERTA DE CLASSES AURACRON ===")
    
    # Tentar diferentes métodos de descoberta
    class_names = [
        'AuracronRealmsBridge',
        'AuracronRigTransformation', 
        'AuracronWorldPartitionBridge',
        'AuracronDynamicRealmSubsystem'
    ]
    
    for class_name in class_names:
        print(f"\n🔍 DEBUGANDO: {class_name}")
        
        # Método 1: find_class simples
        try:
            uclass = unreal.find_class(class_name)
            if uclass:
                print(f"  ✅ find_class('{class_name}'): {uclass}")
                print(f"     └─ Caminho: {uclass.get_path_name()}")
            else:
                print(f"  ❌ find_class('{class_name}'): None")
        except Exception as e:
            print(f"  ❌ find_class('{class_name}'): ERRO - {e}")
        
        # Método 2: Tentar diferentes caminhos de módulo
        module_paths = [
            f"/Script/Auracron.{class_name}",
            f"/Script/AuracronRealmsBridge.{class_name}",
            f"/Script/AuracronMetaHumanBridge.{class_name}",
            f"/Script/AuracronWorldPartitionBridge.{class_name}",
            f"/Script/AuracronDynamicRealmBridge.{class_name}",
            f"/Script/AuracronFoliageBridge.{class_name}"
        ]
        
        for module_path in module_paths:
            try:
                uclass = unreal.load_class(None, module_path)
                if uclass:
                    print(f"  ✅ load_class('{module_path}'): {uclass}")
                    break
            except Exception:
                continue
        else:
            print(f"  ❌ Nenhum caminho de módulo funcionou para {class_name}")
        
        # Método 3: Verificar se existe como atributo do unreal
        try:
            if hasattr(unreal, class_name):
                attr = getattr(unreal, class_name)
                print(f"  ✅ unreal.{class_name}: {attr}")
            else:
                print(f"  ❌ unreal.{class_name}: Não existe como atributo")
        except Exception as e:
            print(f"  ❌ unreal.{class_name}: ERRO - {e}")

def debug_subsystems():
    """Debug específico para subsystems"""
    print("\n=== DEBUG DE SUBSYSTEMS ===")
    
    try:
        editor_world = unreal.EditorLevelLibrary.get_editor_world()
        if not editor_world:
            print("❌ Editor World não disponível")
            return
        
        print(f"✅ Editor World: {editor_world.get_name()}")
        
        # Tentar encontrar AuracronDynamicRealmSubsystem
        subsystem_name = "AuracronDynamicRealmSubsystem"
        
        # Método 1: find_class + get_subsystem
        try:
            subsystem_class = unreal.find_class(subsystem_name)
            if subsystem_class:
                print(f"✅ Classe encontrada: {subsystem_class}")
                subsystem = editor_world.get_subsystem(subsystem_class)
                if subsystem:
                    print(f"✅ Subsystem ativo: {subsystem}")
                else:
                    print(f"⚠️ Subsystem não ativo")
            else:
                print(f"❌ Classe {subsystem_name} não encontrada")
        except Exception as e:
            print(f"❌ Erro ao buscar subsystem: {e}")
        
        # Método 2: Listar todos os subsystems disponíveis
        try:
            print("\n📋 LISTANDO SUBSYSTEMS DISPONÍVEIS:")
            # Não há uma forma direta de listar todos os subsystems, mas podemos tentar alguns conhecidos
            known_subsystems = [
                unreal.UnrealEditorSubsystem,
                unreal.DataLayerEditorSubsystem,
                unreal.WorldPartitionEditorSubsystem,
                unreal.EditorActorSubsystem
            ]
            
            for subsystem_class in known_subsystems:
                try:
                    subsystem = unreal.get_editor_subsystem(subsystem_class)
                    if subsystem:
                        print(f"  ✅ {subsystem_class.__name__}: {subsystem}")
                except Exception as e:
                    print(f"  ❌ {subsystem_class.__name__}: {e}")
                    
        except Exception as e:
            print(f"❌ Erro ao listar subsystems: {e}")
            
    except Exception as e:
        print(f"❌ Erro geral no debug de subsystems: {e}")

def debug_modules():
    """Debug dos módulos carregados"""
    print("\n=== DEBUG DE MÓDULOS ===")
    
    # Verificar se os módulos estão realmente carregados
    # No UE, não há uma forma direta via Python de listar módulos, 
    # mas podemos verificar se conseguimos acessar classes básicas
    
    print("🔍 Verificando acesso a classes básicas do UE:")
    basic_classes = [
        'Actor',
        'ActorComponent', 
        'GameModeBase',
        'PlayerController'
    ]
    
    for class_name in basic_classes:
        try:
            uclass = unreal.find_class(class_name)
            if uclass:
                print(f"  ✅ {class_name}: {uclass}")
            else:
                print(f"  ❌ {class_name}: None")
        except Exception as e:
            print(f"  ❌ {class_name}: ERRO - {e}")

def main():
    """Função principal do debug"""
    print("🐛 INICIANDO DEBUG DE DESCOBERTA DE CLASSES")
    
    try:
        debug_modules()
        debug_class_discovery()
        debug_subsystems()
        
        print("\n" + "="*60)
        print("🏁 DEBUG CONCLUÍDO")
        print("="*60)
        
    except Exception as e:
        print(f"\n💥 ERRO CRÍTICO NO DEBUG: {str(e)}")

if __name__ == "__main__":
    main()
