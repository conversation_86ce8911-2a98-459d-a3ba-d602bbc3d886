// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Realms DinÃ¢micos Bridge Build Configuration
using UnrealBuildTool;
public class AuracronRealmsBridge : ModuleRules
{
    public AuracronRealmsBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicIncludePaths.AddRange(
            new string[] {
                // ... add public include paths required here ...
            }
        );
        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "ModularGameplay",
                "Engine","Landscape",
                "Foliage","ChaosCore","PhysicsCore","Niagara","NiagaraCore",
                "NiagaraShader",
                "RenderCore",
                "RHI","NavigationSystem","GameplayTags",
                "NetCore",
                "ReplicationGraph",
                "DeveloperSettings",
                "EngineSettings",
                "PCG",
                "PCGGeometryScriptInterop",
                "PythonScriptPlugin",
                "Python3"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "DataflowEngine",
                "ChaosSolverEngine",
                "Json",
                "MeshDescription",
                "StaticMeshDescription",
                "GeometryCore",
                "DynamicMesh",
                "GeometryFramework",
                "InteractiveToolsFramework",
                "MetasoundFrontend",
                "MetasoundEngine",
                "PythonScriptPlugin",
                "Python3"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "AssetTools",
                    "BlueprintGraph",
                    "ContentBrowser",
                    "EditorStyle",
                    "EditorWidgets",
                    "Kismet",
                    "KismetCompiler",
                    "LandscapeEditor",
                    "LevelEditor",
                    "PropertyEditor",
                    "ToolMenus",
                    "UnrealEd",
                    "WorldBrowser",
                    "PlacementMode",
                    "EditorInteractiveToolsFramework",
                    "MeshModelingTools",
                    "MeshModelingToolsExp"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {
                // ... add any modules that your module loads dynamically here ...
            }
        );
        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.InShippingBuildsOnly;
            bUseUnity = true;
        }
        // Enable additional features for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_REALMS_DEBUG=1");
            PublicDefinitions.Add("AURACRON_REALMS_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_REALMS_DEBUG=0");
            PublicDefinitions.Add("AURACRON_REALMS_PROFILING=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=1");
            // Mobile-specific optimizations
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=0");
        }
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_WORLD_PARTITION=1");
        PublicDefinitions.Add("WITH_PCG=1");
        PublicDefinitions.Add("WITH_CHAOS_PHYSICS=1");
        PublicDefinitions.Add("WITH_NANITE=1");
        PublicDefinitions.Add("WITH_LUMEN=1");
    }
}



