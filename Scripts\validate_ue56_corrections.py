#!/usr/bin/env python3
"""
Script de Validação das Correções UE 5.6
Valida se todas as correções aplicadas aos bridges Auracron funcionam corretamente
"""

import unreal
import sys

def print_header(title):
    """Imprime cabeçalho formatado"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_test_result(test_name, success, details=""):
    """Imprime resultado do teste formatado"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")

def validate_editor_subsystems():
    """Valida se os subsistemas de editor estão funcionando"""
    print_header("VALIDAÇÃO DOS SUBSISTEMAS DE EDITOR")
    
    tests_passed = 0
    total_tests = 3
    
    # Teste 1: DataLayerEditorSubsystem
    try:
        data_layer_subsystem = unreal.get_editor_subsystem(unreal.DataLayerEditorSubsystem)
        success = data_layer_subsystem is not None
        print_test_result("DataLayerEditorSubsystem", success, 
                         f"Subsistema: {type(data_layer_subsystem).__name__}" if success else "Não encontrado")
        if success:
            tests_passed += 1
    except Exception as e:
        print_test_result("DataLayerEditorSubsystem", False, f"Erro: {e}")
    
    # Teste 2: WorldPartitionEditorSubsystem
    try:
        wp_subsystem = unreal.get_editor_subsystem(unreal.WorldPartitionEditorSubsystem)
        success = wp_subsystem is not None
        print_test_result("WorldPartitionEditorSubsystem", success,
                         f"Subsistema: {type(wp_subsystem).__name__}" if success else "Não encontrado")
        if success:
            tests_passed += 1
    except Exception as e:
        print_test_result("WorldPartitionEditorSubsystem", False, f"Erro: {e}")
    
    # Teste 3: get_editor_subsystem vs get_world_subsystem
    try:
        # Verificar se get_editor_subsystem funciona
        editor_subsystem_works = hasattr(unreal, 'get_editor_subsystem')
        print_test_result("get_editor_subsystem disponível", editor_subsystem_works,
                         "API correta do UE 5.6" if editor_subsystem_works else "API não encontrada")
        if editor_subsystem_works:
            tests_passed += 1
    except Exception as e:
        print_test_result("get_editor_subsystem disponível", False, f"Erro: {e}")
    
    return tests_passed, total_tests

def validate_enums():
    """Valida se os enums estão funcionando corretamente"""
    print_header("VALIDAÇÃO DOS ENUMS")
    
    tests_passed = 0
    total_tests = 2
    
    # Teste 1: DataLayerType enum
    try:
        runtime_type = unreal.DataLayerType.RUNTIME
        editor_type = unreal.DataLayerType.EDITOR
        success = runtime_type is not None and editor_type is not None
        print_test_result("DataLayerType enum", success,
                         f"RUNTIME: {runtime_type}, EDITOR: {editor_type}" if success else "Enum não encontrado")
        if success:
            tests_passed += 1
    except Exception as e:
        print_test_result("DataLayerType enum", False, f"Erro: {e}")
    
    # Teste 2: Verificar se enum pode ser usado em funções
    try:
        layer_type = unreal.DataLayerType.RUNTIME
        # Verificar se é um enum válido
        success = hasattr(layer_type, 'name') or str(layer_type) != str(0)
        print_test_result("DataLayerType utilizável", success,
                         f"Tipo: {type(layer_type)}, Valor: {layer_type}" if success else "Enum não utilizável")
        if success:
            tests_passed += 1
    except Exception as e:
        print_test_result("DataLayerType utilizável", False, f"Erro: {e}")
    
    return tests_passed, total_tests

def validate_bridges():
    """Valida se os bridges estão sendo reconhecidos corretamente"""
    print_header("VALIDAÇÃO DOS BRIDGES")
    
    tests_passed = 0
    total_tests = 4
    
    bridges_to_test = [
        'AuracronRealmsBridge',
        'AuracronDataLayerManager', 
        'AuracronLandscapeManager',
        'AuracronWorldPartitionBridge'
    ]
    
    for bridge_name in bridges_to_test:
        try:
            bridge_class = getattr(unreal, bridge_name, None)
            success = bridge_class is not None
            
            if success:
                # Verificar tipo do bridge
                bridge_type = "Component" if "Component" in str(type(bridge_class)) else "Class"
                print_test_result(f"{bridge_name}", success, f"Tipo: {bridge_type}")
                tests_passed += 1
            else:
                print_test_result(f"{bridge_name}", success, "Bridge não encontrado")
                
        except Exception as e:
            print_test_result(f"{bridge_name}", False, f"Erro: {e}")
    
    return tests_passed, total_tests

def validate_property_access():
    """Valida se o acesso a propriedades está funcionando"""
    print_header("VALIDAÇÃO DO ACESSO A PROPRIEDADES")
    
    tests_passed = 0
    total_tests = 2
    
    # Teste 1: set_editor_property disponível
    try:
        # Criar um objeto de teste para verificar set_editor_property
        test_object = unreal.EditorAssetLibrary
        has_set_property = hasattr(test_object, 'set_editor_property') or callable(getattr(test_object, 'set_editor_property', None))
        print_test_result("set_editor_property disponível", has_set_property,
                         "Método disponível para configurar propriedades" if has_set_property else "Método não encontrado")
        if has_set_property:
            tests_passed += 1
    except Exception as e:
        print_test_result("set_editor_property disponível", False, f"Erro: {e}")
    
    # Teste 2: hasattr funcionando para verificação de propriedades
    try:
        # Testar hasattr com um objeto conhecido
        test_success = hasattr(unreal, 'EditorAssetLibrary')
        print_test_result("hasattr funcionando", test_success,
                         "Verificação de propriedades funciona" if test_success else "hasattr não funciona")
        if test_success:
            tests_passed += 1
    except Exception as e:
        print_test_result("hasattr funcionando", False, f"Erro: {e}")
    
    return tests_passed, total_tests

def main():
    """Função principal de validação"""
    print_header("VALIDAÇÃO DAS CORREÇÕES UE 5.6 - BRIDGES AURACRON")
    print("Validando se todas as correções aplicadas estão funcionando corretamente...")
    
    total_passed = 0
    total_tests = 0
    
    # Executar todas as validações
    passed, tests = validate_editor_subsystems()
    total_passed += passed
    total_tests += tests
    
    passed, tests = validate_enums()
    total_passed += passed
    total_tests += tests
    
    passed, tests = validate_bridges()
    total_passed += passed
    total_tests += tests
    
    passed, tests = validate_property_access()
    total_passed += passed
    total_tests += tests
    
    # Resultado final
    print_header("RESULTADO FINAL")
    success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"Testes Executados: {total_tests}")
    print(f"Testes Aprovados: {total_passed}")
    print(f"Testes Falharam: {total_tests - total_passed}")
    print(f"Taxa de Sucesso: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("\n🎉 VALIDAÇÃO APROVADA - Correções funcionando corretamente!")
        return True
    else:
        print("\n⚠️  VALIDAÇÃO REPROVADA - Algumas correções precisam de ajustes")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
