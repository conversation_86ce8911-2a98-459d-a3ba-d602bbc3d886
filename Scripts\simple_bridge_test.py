#!/usr/bin/env python3
"""
Simple test to check Auracron bridge status
"""

import unreal
import os

def main():
    print("=== AURACRON BRIDGE STATUS TEST ===")
    
    # Test basic bridge classes with their correct module paths
    bridges_to_test = [
        ("AuracronRealmsBridge", "/Script/AuracronRealmsBridge.AuracronRealmsBridge"),
        ("AuracronWorldPartitionBridge", "/Script/AuracronWorldPartitionBridge.AuracronWorldPartitionBridge"),
        ("AuracronFoliageBridge", "/Script/AuracronFoliageBridge.AuracronFoliageBridge"),
        ("AuracronDynamicRealmBridge", "/Script/AuracronDynamicRealmBridge.AuracronDynamicRealmBridge"),
        ("AuracronCombatBridge", "/Script/AuracronCombatBridge.AuracronCombatBridge"),
        ("AuracronPCGBridge", "/Script/AuracronPCGBridge.AuracronPCGBridge"),
        ("AuracronLumenBridge", "/Script/AuracronLumenBridge.AuracronLumenBridge"),
        ("AuracronNaniteBridge", "/Script/AuracronNaniteBridge.AuracronNaniteBridge"),
        ("AuracronVFXBridge", "/Script/AuracronVFXBridge.AuracronVFXBridge"),
        ("AuracronHarmonyEngineBridge", "/Script/AuracronHarmonyEngineBridge.AuracronHarmonyEngineBridge")
    ]

    results = []

    for bridge_name, bridge_path in bridges_to_test:
        try:
            # Use the correct UE 5.6 Python API
            bridge_class = unreal.load_class(None, bridge_path)
            if bridge_class:
                status = f"✅ {bridge_name}: FOUND"
                results.append(f"LOADED: {bridge_name}")
            else:
                status = f"❌ {bridge_name}: NOT FOUND"
                results.append(f"FAILED: {bridge_name}")
        except Exception as e:
            status = f"❌ {bridge_name}: ERROR - {str(e)}"
            results.append(f"ERROR: {bridge_name} - {str(e)}")

        print(status)
    
    # Test subsystem
    print("\n=== SUBSYSTEM TEST ===")
    try:
        editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
        world = editor_subsystem.get_editor_world()
        
        if world:
            print(f"✅ World: {world.get_name()}")
            
            # Test AuracronDynamicRealmSubsystem
            try:
                # Use the correct UE 5.6 Python API for subsystems
                # Try as world subsystem first
                subsystem_class = unreal.load_class(None, "/Script/AuracronDynamicRealmBridge.AuracronDynamicRealmSubsystem")
                if subsystem_class:
                    subsystem = world.get_subsystem(subsystem_class)
                    if subsystem:
                        print(f"✅ AuracronDynamicRealmSubsystem: ACTIVE (World Subsystem)")
                        results.append("SUBSYSTEM: AuracronDynamicRealmSubsystem - ACTIVE")
                    else:
                        # Try as editor subsystem if world subsystem fails
                        try:
                            subsystem = unreal.get_editor_subsystem(subsystem_class)
                            if subsystem:
                                print(f"✅ AuracronDynamicRealmSubsystem: ACTIVE (Editor Subsystem)")
                                results.append("SUBSYSTEM: AuracronDynamicRealmSubsystem - ACTIVE")
                            else:
                                print(f"⚠️ AuracronDynamicRealmSubsystem: CLASS FOUND BUT NOT ACTIVE")
                                results.append("SUBSYSTEM: AuracronDynamicRealmSubsystem - INACTIVE")
                        except Exception:
                            print(f"⚠️ AuracronDynamicRealmSubsystem: CLASS FOUND BUT NOT ACTIVE")
                            results.append("SUBSYSTEM: AuracronDynamicRealmSubsystem - INACTIVE")
                else:
                    print(f"❌ AuracronDynamicRealmSubsystem: CLASS NOT FOUND")
                    results.append("SUBSYSTEM: AuracronDynamicRealmSubsystem - NOT FOUND")
            except Exception as e:
                print(f"❌ AuracronDynamicRealmSubsystem: ERROR - {str(e)}")
                results.append(f"SUBSYSTEM: AuracronDynamicRealmSubsystem - ERROR: {str(e)}")
        else:
            print("❌ No editor world found")
            results.append("WORLD: NOT FOUND")
            
    except Exception as e:
        print(f"❌ Subsystem test failed: {str(e)}")
        results.append(f"SUBSYSTEM_TEST: ERROR - {str(e)}")
    
    # Write results to file
    try:
        with open("c:/Aura/projeto/Auracron/bridge_test_results.txt", "w") as f:
            f.write("=== AURACRON BRIDGE TEST RESULTS ===\n\n")
            for result in results:
                f.write(f"{result}\n")
            f.write("\n=== TEST COMPLETE ===\n")
        print("\n✅ Results saved to bridge_test_results.txt")
    except Exception as e:
        print(f"\n❌ Failed to save results: {str(e)}")
    
    print("\n=== TEST COMPLETE ===")

if __name__ == "__main__":
    main()
