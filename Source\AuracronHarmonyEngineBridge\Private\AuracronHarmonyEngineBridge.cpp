#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineSubsystem.h"
#include "GameplayTagsManager.h"
#include "Engine/Engine.h"
#include "Modules/ModuleManager.h"

DEFINE_LOG_CATEGORY(LogHarmonyEngine);

// Static member initialization
UAuracronHarmonyEngineBridge* UAuracronHarmonyEngineBridge::Instance = nullptr;

// UAuracronHarmonyEngineBridge Implementation
UAuracronHarmonyEngineBridge::UAuracronHarmonyEngineBridge()
{
    // Default constructor
}

UAuracronHarmonyEngineBridge::UAuracronHarmonyEngineBridge(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Constructor with ObjectInitializer
}

UAuracronHarmonyEngineBridge* UAuracronHarmonyEngineBridge::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronHarmonyEngineBridge>(GetTransientPackage(), UAuracronHarmonyEngineBridge::StaticClass());
        if (Instance)
        {
            Instance->AddToRoot(); // Prevent garbage collection
        }
    }
    return Instance;
}

UHarmonyEngineSubsystem* UAuracronHarmonyEngineBridge::GetSubsystem(const UObject* WorldContext)
{
    if (const UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull))
    {
        return World->GetSubsystem<UHarmonyEngineSubsystem>();
    }
    return nullptr;
}

// Define Harmony Engine Gameplay Tags - initialized in StartupModule to avoid static initialization crashes
namespace HarmonyEngineGameplayTags
{
    // Behavior Detection Tags
    FGameplayTag Behavior_Positive;
    FGameplayTag Behavior_Toxic;
    FGameplayTag Behavior_Healing;
    FGameplayTag Behavior_Mentoring;

    // Emotional State Tags
    FGameplayTag Emotion_Happy;
    FGameplayTag Emotion_Frustrated;
    FGameplayTag Emotion_Angry;
    FGameplayTag Emotion_Calm;

    // Intervention Tags
    FGameplayTag Intervention_Gentle;
    FGameplayTag Intervention_Moderate;
    FGameplayTag Intervention_Strong;
    FGameplayTag Intervention_Emergency;

    // Reward Tags
    FGameplayTag Reward_Kindness;
    FGameplayTag Reward_Mentorship;
    FGameplayTag Reward_CommunityHero;
    FGameplayTag Reward_Healing;
}

// Module singleton instance
FAuracronHarmonyEngineBridgeModule* FAuracronHarmonyEngineBridgeModule::ModuleInstance = nullptr;

void FAuracronHarmonyEngineBridgeModule::StartupModule()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Starting Auracron Harmony Engine Bridge Module"));

    // Set module instance
    ModuleInstance = this;

    // Register gameplay tags first
    RegisterHarmonyGameplayTags();

    // Initialize GameplayTags safely after registration
    InitializeGameplayTags();

    // Initialize Harmony Engine systems
    InitializeHarmonyEngine();
    
    // Initialize ML models
    InitializeMachineLearningModels();
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Auracron Harmony Engine Bridge Module started successfully"));
}

void FAuracronHarmonyEngineBridgeModule::ShutdownModule()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Shutting down Auracron Harmony Engine Bridge Module"));
    
    // Cleanup Harmony Engine systems
    CleanupHarmonyEngine();
    
    // Clear module instance
    ModuleInstance = nullptr;
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Auracron Harmony Engine Bridge Module shut down"));
}

FAuracronHarmonyEngineBridgeModule& FAuracronHarmonyEngineBridgeModule::Get()
{
    if (ModuleInstance)
    {
        return *ModuleInstance;
    }
    
    return FModuleManager::LoadModuleChecked<FAuracronHarmonyEngineBridgeModule>("AuracronHarmonyEngineBridge");
}

bool FAuracronHarmonyEngineBridgeModule::IsAvailable()
{
    return ModuleInstance != nullptr;
}

void FAuracronHarmonyEngineBridgeModule::InitializeHarmonyEngine()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initializing Harmony Engine core systems"));
    
    // Verify that required subsystems are available
    if (GEngine)
    {
        // Register with engine for game instance creation callbacks
        FWorldDelegates::OnPostWorldInitialization.AddLambda([](UWorld* World, const UWorld::InitializationValues IVS)
        {
            if (World && World->GetGameInstance())
            {
                // Ensure HarmonyEngineSubsystem is created
                UHarmonyEngineSubsystem* HarmonySubsystem = World->GetSubsystem<UHarmonyEngineSubsystem>();
                if (HarmonySubsystem)
                {
                    UE_LOG(LogHarmonyEngine, Log, TEXT("HarmonyEngineSubsystem initialized for world: %s"), *World->GetName());
                }
            }
        });
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine core systems initialized"));
}

void FAuracronHarmonyEngineBridgeModule::CleanupHarmonyEngine()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Cleaning up Harmony Engine systems"));
    
    // Cleanup would be handled by individual subsystems and components
    // during their respective shutdown procedures
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine systems cleaned up"));
}

void FAuracronHarmonyEngineBridgeModule::RegisterHarmonyGameplayTags()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Registering Harmony Engine gameplay tags"));
    
    // Get the gameplay tags manager
    UGameplayTagsManager& TagsManager = UGameplayTagsManager::Get();
    
    // Register behavior tags
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Behavior.Positive"), TEXT("Positive player behavior detected"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Behavior.Toxic"), TEXT("Toxic player behavior detected"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Behavior.Healing"), TEXT("Community healing behavior"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Behavior.Mentoring"), TEXT("Mentoring behavior detected"));
    
    // Register emotional state tags
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Emotion.Happy"), TEXT("Player is in happy emotional state"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Emotion.Frustrated"), TEXT("Player is frustrated"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Emotion.Angry"), TEXT("Player is angry"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Emotion.Calm"), TEXT("Player is calm"));
    
    // Register intervention tags
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Intervention.Gentle"), TEXT("Gentle intervention applied"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Intervention.Moderate"), TEXT("Moderate intervention applied"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Intervention.Strong"), TEXT("Strong intervention applied"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Intervention.Emergency"), TEXT("Emergency intervention applied"));
    
    // Register reward tags
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Reward.Kindness"), TEXT("Kindness reward"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Reward.Mentorship"), TEXT("Mentorship reward"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Reward.CommunityHero"), TEXT("Community hero reward"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Reward.Healing"), TEXT("Healing reward"));
    
    // Register tier tags
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Tier.Bronze"), TEXT("Bronze tier player"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Tier.Silver"), TEXT("Silver tier player"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Tier.Gold"), TEXT("Gold tier player"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Tier.Platinum"), TEXT("Platinum tier player"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Tier.Diamond"), TEXT("Diamond tier player"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Tier.Legendary"), TEXT("Legendary tier player"));
    
    // Register status tags
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Status.Monitoring"), TEXT("Player under behavioral monitoring"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Status.Intervention"), TEXT("Player under intervention"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Status.Healing"), TEXT("Player in healing session"));
    TagsManager.AddNativeGameplayTag(FName("HarmonyEngine.Status.Mentoring"), TEXT("Player in mentoring session"));
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine gameplay tags registered successfully"));
}

void FAuracronHarmonyEngineBridgeModule::InitializeMachineLearningModels()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initializing Harmony Engine ML models"));
    
    // Initialize behavioral prediction models
    // In a full implementation, this would load pre-trained models or initialize training
    
    // Create default model configurations
    FString MLConfigPath = FPaths::ProjectContentDir() + TEXT("HarmonyEngine/MLModels/");
    
    // Ensure ML model directory exists
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*MLConfigPath))
    {
        PlatformFile.CreateDirectoryTree(*MLConfigPath);
        UE_LOG(LogHarmonyEngine, Log, TEXT("Created ML models directory: %s"), *MLConfigPath);
    }
    
    // Initialize model configurations
    FString ModelConfig = TEXT("{\n");
    ModelConfig += TEXT("  \"behavioral_prediction_model\": {\n");
    ModelConfig += TEXT("    \"version\": \"1.0\",\n");
    ModelConfig += TEXT("    \"accuracy_threshold\": 0.75,\n");
    ModelConfig += TEXT("    \"training_interval\": 300,\n");
    ModelConfig += TEXT("    \"features\": [\"toxicity\", \"positivity\", \"frustration\", \"session_duration\"]\n");
    ModelConfig += TEXT("  },\n");
    ModelConfig += TEXT("  \"emotional_prediction_model\": {\n");
    ModelConfig += TEXT("    \"version\": \"1.0\",\n");
    ModelConfig += TEXT("    \"accuracy_threshold\": 0.70,\n");
    ModelConfig += TEXT("    \"training_interval\": 600,\n");
    ModelConfig += TEXT("    \"features\": [\"emotional_history\", \"behavior_patterns\", \"social_interactions\"]\n");
    ModelConfig += TEXT("  }\n");
    ModelConfig += TEXT("}\n");
    
    FString ConfigFilePath = MLConfigPath + TEXT("model_config.json");
    FFileHelper::SaveStringToFile(ModelConfig, *ConfigFilePath);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("ML models initialized with configuration saved to: %s"), *ConfigFilePath);
}

void FAuracronHarmonyEngineBridgeModule::InitializeGameplayTags()
{
    // Initialize GameplayTags safely during module startup
    using namespace HarmonyEngineGameplayTags;

    // Behavior Detection Tags
    Behavior_Positive = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Behavior.Positive"));
    Behavior_Toxic = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Behavior.Toxic"));
    Behavior_Healing = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Behavior.Healing"));
    Behavior_Mentoring = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Behavior.Mentoring"));

    // Emotional State Tags
    Emotion_Happy = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Emotion.Happy"));
    Emotion_Frustrated = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Emotion.Frustrated"));
    Emotion_Angry = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Emotion.Angry"));
    Emotion_Calm = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Emotion.Calm"));

    // Intervention Tags
    Intervention_Gentle = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Intervention.Gentle"));
    Intervention_Moderate = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Intervention.Moderate"));
    Intervention_Strong = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Intervention.Strong"));
    Intervention_Emergency = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Intervention.Emergency"));

    // Reward Tags
    Reward_Kindness = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Reward.Kindness"));
    Reward_Mentorship = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Reward.Mentorship"));
    Reward_CommunityHero = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Reward.CommunityHero"));
    Reward_Healing = FGameplayTag::RequestGameplayTag(FName("HarmonyEngine.Reward.Healing"));

    UE_LOG(LogHarmonyEngine, Log, TEXT("GameplayTags initialized successfully"));
}

// Implement the module
IMPLEMENT_MODULE(FAuracronHarmonyEngineBridgeModule, AuracronHarmonyEngineBridge)
