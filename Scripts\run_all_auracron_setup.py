#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script principal para executar toda a configuração do Auracron
Executa dentro do Unreal Engine 5.6
"""

import unreal
import sys
import os

def run_script(script_name, description):
    """Executa um script Python e retorna o resultado"""
    try:
        print(f"\n{'='*60}")
        print(f"EXECUTANDO: {description}")
        print(f"Script: {script_name}")
        print('='*60)
        
        # Importar e executar o script
        script_path = os.path.join(os.path.dirname(__file__), script_name)
        
        if not os.path.exists(script_path):
            print(f"❌ ERRO: Script não encontrado: {script_path}")
            return False
            
        # Executar o script
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
            
        # Executar no contexto atual
        exec(script_content, globals())
        
        print(f"✅ {description}: CONCLUÍDO")
        return True
        
    except Exception as e:
        print(f"❌ ERRO em {description}: {str(e)}")
        return False

def main():
    """Função principal para executar toda a configuração do Auracron"""
    print("🚀 INICIANDO CONFIGURAÇÃO COMPLETA DO AURACRON")
    print("Este processo irá:")
    print("1. Testar a compilação dos módulos")
    print("2. Criar curves necessários")
    print("3. Criar a base da Planície Radiante")
    print("4. Configurar o sistema de realms")
    
    scripts_to_run = [
        {
            'script': 'create_vertical_connector_curves.py',
            'description': 'Criação de Curves para Conectores Verticais',
            'critical': False
        },
        {
            'script': 'create_planicie_radiante_base.py',
            'description': 'Criação da Base da Planície Radiante',
            'critical': False
        }
    ]
    
    results = []
    critical_failures = 0
    
    for script_info in scripts_to_run:
        success = run_script(script_info['script'], script_info['description'])
        results.append({
            'script': script_info['script'],
            'description': script_info['description'],
            'success': success,
            'critical': script_info['critical']
        })
        
        if not success and script_info['critical']:
            critical_failures += 1
    
    # Relatório final
    print(f"\n{'='*60}")
    print("RELATÓRIO FINAL DA CONFIGURAÇÃO")
    print('='*60)
    
    successful = 0
    failed = 0
    
    for result in results:
        status = "✅ SUCESSO" if result['success'] else "❌ FALHA"
        critical_mark = " (CRÍTICO)" if result['critical'] else ""
        print(f"{status} {result['description']}{critical_mark}")
        
        if result['success']:
            successful += 1
        else:
            failed += 1
    
    print(f"\n📊 RESUMO:")
    print(f"  Sucessos: {successful}")
    print(f"  Falhas: {failed}")
    print(f"  Falhas Críticas: {critical_failures}")
    
    if critical_failures == 0:
        if failed == 0:
            print(f"\n🎉 CONFIGURAÇÃO COMPLETA!")
            print("Todos os scripts foram executados com sucesso!")
            print("\nO projeto Auracron está pronto para uso!")
        else:
            print(f"\n⚠️ CONFIGURAÇÃO PARCIAL")
            print("Alguns scripts falharam, mas nenhum crítico.")
            print("O projeto pode funcionar, mas algumas funcionalidades podem estar limitadas.")
    else:
        print(f"\n💥 CONFIGURAÇÃO FALHOU")
        print("Falhas críticas detectadas. O projeto pode não funcionar corretamente.")
        print("Corrija os problemas críticos antes de continuar.")
    
    # Instruções finais
    print(f"\n📋 PRÓXIMOS PASSOS:")
    if critical_failures == 0:
        print("1. Abra o projeto no Unreal Engine 5.6")
        print("2. Verifique se todos os assets foram criados corretamente")
        print("3. Teste as funcionalidades básicas do sistema de realms")
        print("4. Execute testes adicionais conforme necessário")
    else:
        print("1. Revise os erros críticos reportados acima")
        print("2. Corrija os problemas de compilação/configuração")
        print("3. Execute este script novamente")
        print("4. Verifique a documentação para troubleshooting")
    
    return critical_failures == 0

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'='*60}")
        if success:
            print("🏁 CONFIGURAÇÃO DO AURACRON FINALIZADA COM SUCESSO!")
        else:
            print("🚨 CONFIGURAÇÃO DO AURACRON FALHOU!")
        print('='*60)
        
    except Exception as e:
        print(f"\n💥 ERRO CRÍTICO NA CONFIGURAÇÃO: {str(e)}")
        print("Verifique os logs acima para mais detalhes.")
        success = False
    
    # Não usar exit() no contexto do UE
    if not success:
        print("\n⚠️ Processo finalizado com erros.")
