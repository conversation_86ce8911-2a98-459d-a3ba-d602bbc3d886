// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - World Partition Bridge Build Configuration
// Production-ready build file for UE5.6 World Partition API bridge
using UnrealBuildTool;
using System.IO;
public class AuracronWorldPartitionBridge : ModuleRules
{
    public AuracronWorldPartitionBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        // Module type and optimization settings
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        bUseUnity = true;
        IWYUSupport = IWYUSupport.Full;
        // Core dependencies for World Partition functionality
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "InputCore",
                "EngineSettings",
                "RenderCore",
                "RHI",
                "Landscape",
                "AudioMixer",
                "NiagaraCore",
                "MeshUtilitiesCommon"
            }
        );
        // World Partition specific dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "MeshDescription",
            "StaticMeshDescription",
            "Landscape",
            "Foliage",
            "NavigationSystem",
            "Navmesh",
            "UMG"
        });
        // Private dependencies for internal functionality
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "GameplayTags",
                "Json",
                "Json",
                "ImageWrapper",
                "RawMesh",
                "GeometryCore",
                "DynamicMesh",
                "GeometryFramework",
                "InteractiveToolsFramework",
                "MeshConversion",
                "ModelingComponents",

                "DeveloperSettings",
                "XmlParser",
                "Localization"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "AssetTools",
                    "AudioEditor",
                    "ContentBrowser",
                    "EditorStyle",
                    "EditorWidgets",
                    "GraphEditor",
                    "KismetCompiler",
                    "MeshPaint",
                    "SourceControl",
                    "ToolMenus",
                    "DataLayerEditor",
                    "LevelInstanceEditor",
                    "WorldPartitionHLODUtilities",
                    "MeshUtilities",
                    "MeshMergeUtilities",
                    "HierarchicalLODUtilities",
                    "MeshBuilder",
                    "MaterialUtilities",
                    "ContentBrowserData",
                    "ModelingComponentsEditorOnly",
                    "AddContentDialog",
                    "MeshReductionInterface"
                }
            );
        }
        // Python integration dependencies
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "PythonScriptPlugin",
                "Python3"
            });
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_WIN64=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_ANDROID=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_IOS=1");
        }
        // Preprocessor definitions for World Partition features
        PublicDefinitions.AddRange(new string[]
        {
            "WITH_WORLD_PARTITION_BRIDGE=1",
            "WITH_WORLD_PARTITION_STREAMING=1",
            "WITH_WORLD_PARTITION_HLOD=1",
            "WITH_WORLD_PARTITION_DATA_LAYERS=1",
            "WITH_WORLD_PARTITION_RUNTIME_HASH=1",
            "WITH_WORLD_PARTITION_EDITOR_HASH=1",
            "WITH_WORLD_PARTITION_MINIMAP=1",
            "WITH_WORLD_PARTITION_LOCATION_VOLUMES=1",
            "WITH_WORLD_PARTITION_STREAMING_SOURCE=1",
            "WITH_WORLD_PARTITION_GRID_PREVIEW=1",
            "WITH_WORLD_PARTITION_DEBUG_VISUALIZATION=1",
            "WITH_WORLD_PARTITION_COMMANDLETS=1",
            "WITH_WORLD_PARTITION_BUILDER=1",
            "WITH_WORLD_PARTITION_COOK_SUPPORT=1",
            "WITH_WORLD_PARTITION_VALIDATION=1",
            "WITH_PYTHON_BINDINGS=1",
            "WITH_AURACRON_WORLD_PARTITION_EXTENSIONS=1"
        });
        // Include paths for World Partition headers
        PublicIncludePaths.AddRange(new string[]
        {
            Path.Combine(ModuleDirectory, "Public"),
            Path.Combine(EngineDirectory, "Source/Runtime/Engine/Public/WorldPartition"),
            Path.Combine(EngineDirectory, "Source/Editor/WorldPartitionEditor/Public"),
            Path.Combine(EngineDirectory, "Source/Editor/WorldPartitionEditor/Private"),
            Path.Combine(EngineDirectory, "Source/Runtime/Engine/Public/WorldPartition"),
            Path.Combine(EngineDirectory, "Source/Runtime/Engine/Private/WorldPartition")
        });
        PrivateIncludePaths.AddRange(new string[]
        {
            Path.Combine(ModuleDirectory, "Private"),
            Path.Combine(EngineDirectory, "Source/Editor/UnrealEd/Private"),
            Path.Combine(EngineDirectory, "Source/Editor/LevelEditor/Private"),
            Path.Combine(EngineDirectory, "Source/Runtime/Renderer/Private")
        });
        // Compiler and linker settings
        bEnableExceptions = true;
        bUseRTTI = true;
        CppCompileWarningSettings.ShadowVariableWarningLevel = WarningLevel.Warning;
        CppCompileWarningSettings.UnsafeTypeCastWarningLevel = WarningLevel.Warning;
        // Development and shipping build optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Development ||
            Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_OPTIMIZED=1");
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        // Debug build settings
        if (Target.Configuration == UnrealTargetConfiguration.Debug ||
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_DEBUG=1");
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_VERBOSE_LOGGING=1");
        }
        // Editor-only features
        if (Target.bBuildEditor)
        {
            PublicDefinitions.Add("WITH_WORLD_PARTITION_BRIDGE_EDITOR=1");
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "LevelEditor",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "GraphEditor",
                "KismetCompiler",
                "ToolMenus",
                "AssetTools",
                "ContentBrowser",
                "EditorWidgets",
                "Slate",
                "SlateCore",
                "EditorStyle",
                "ToolWidgets",
                "WorkspaceMenuStructure",
                "EditorSubsystem"
            });
        }
        // Ensure compatibility with UE5.6 World Partition system
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2=0");
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3=0");
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4=0");
        PublicDefinitions.Add("UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5=0");
    }
}




