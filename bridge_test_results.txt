=== AURACRON BRIDGE TEST RESULTS ===

LOADED: AuracronRealmsBridge
LOADED: AuracronWorldPartitionBridge
LOADED: AuracronFoliageBridge
LOADED: AuracronDynamicRealmBridge
LOADED: AuracronCombatBridge
LOADED: AuracronPCGBridge
LOADED: AuracronLumenBridge
LOADED: AuracronNaniteBridge
LOADED: AuracronVFXBridge
LOADED: AuracronHarmonyEngineBridge
SUBSYSTEM: AuracronDynamicRealmSubsystem - ERROR: 'World' object has no attribute 'get_subsystem'

=== TEST COMPLETE ===
