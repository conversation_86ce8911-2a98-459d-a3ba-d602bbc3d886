#!/usr/bin/env python3
"""
Test script to check which Auracron bridges are loading properly
"""

import unreal

def test_bridge_loading():
    """Test which bridges are available and working"""
    print("=== AURACRON BRIDGE LOADING TEST ===")
    
    # List of expected bridges
    expected_bridges = [
        "AuracronRealmsBridge",
        "AuracronWorldPartitionBridge", 
        "AuracronFoliageBridge",
        "AuracronDynamicRealmBridge",
        "AuracronCombatBridge",
        "AuracronPCGBridge",
        "AuracronLumenBridge",
        "AuracronNaniteBridge",
        "AuracronVFXBridge",
        "AuracronHarmonyEngineBridge"
    ]
    
    loaded_bridges = []
    failed_bridges = []
    
    for bridge_name in expected_bridges:
        try:
            # Try to find the bridge class
            bridge_class = unreal.find_class(bridge_name)
            if bridge_class:
                print(f"✅ {bridge_name}: Class found")
                loaded_bridges.append(bridge_name)
            else:
                print(f"❌ {bridge_name}: Class not found")
                failed_bridges.append(bridge_name)
        except Exception as e:
            print(f"❌ {bridge_name}: Error - {str(e)}")
            failed_bridges.append(bridge_name)
    
    print(f"\n=== SUMMARY ===")
    print(f"Loaded bridges: {len(loaded_bridges)}")
    print(f"Failed bridges: {len(failed_bridges)}")
    
    if failed_bridges:
        print(f"\nFailed bridges: {', '.join(failed_bridges)}")
    
    return loaded_bridges, failed_bridges

def test_subsystems():
    """Test which subsystems are available"""
    print("\n=== SUBSYSTEM TEST ===")

    try:
        # Use the modern API instead of deprecated EditorLevelLibrary
        editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
        world = editor_subsystem.get_editor_world()

        if world:
            print(f"✅ World found: {world.get_name()}")

            # Try to get the subsystem using correct UE 5.6 APIs
            try:
                subsystem_class = unreal.find_class("AuracronDynamicRealmSubsystem")
                if subsystem_class:
                    # Try world subsystem first
                    subsystem = world.get_subsystem(subsystem_class)
                    if subsystem:
                        print(f"✅ AuracronDynamicRealmSubsystem: Found and accessible (World Subsystem)")
                        print(f"   - Class: {subsystem.get_class().get_name()}")
                        print(f"   - Is Initialized: {subsystem.is_initialized() if hasattr(subsystem, 'is_initialized') else 'Unknown'}")
                    else:
                        # Try editor subsystem as fallback
                        try:
                            subsystem = unreal.get_editor_subsystem(subsystem_class)
                            if subsystem:
                                print(f"✅ AuracronDynamicRealmSubsystem: Found and accessible (Editor Subsystem)")
                                print(f"   - Class: {subsystem.get_class().get_name()}")
                                print(f"   - Is Initialized: {subsystem.is_initialized() if hasattr(subsystem, 'is_initialized') else 'Unknown'}")
                            else:
                                print(f"❌ AuracronDynamicRealmSubsystem: Subsystem instance not found")
                        except Exception as e:
                            print(f"❌ AuracronDynamicRealmSubsystem: Editor subsystem failed - {str(e)}")
                else:
                    print(f"❌ AuracronDynamicRealmSubsystem: Class not found")
            except Exception as e:
                print(f"❌ AuracronDynamicRealmSubsystem: Error - {str(e)}")
        else:
            print("❌ No editor world found")

    except Exception as e:
        print(f"❌ Subsystem test failed: {str(e)}")

def test_python_bindings():
    """Test Python bindings for bridges"""
    print("\n=== PYTHON BINDINGS TEST ===")
    
    # Test if we can access bridge APIs through Python
    test_apis = [
        "AuracronWorldPartitionBridgeAPI",
        "AuracronFoliageBridgeAPI", 
        "AuracronLumenBridgeAPI",
        "AuracronRealmsBridge"
    ]
    
    for api_name in test_apis:
        try:
            api_class = unreal.find_class(api_name)
            if api_class:
                print(f"✅ {api_name}: Python binding available")
            else:
                print(f"❌ {api_name}: Python binding not found")
        except Exception as e:
            print(f"❌ {api_name}: Error - {str(e)}")

if __name__ == "__main__":
    # Run all tests and save results to file
    print("=== AURACRON BRIDGE LOADING TEST ===")

    loaded_bridges, failed_bridges = test_bridge_loading()
    test_subsystems()
    test_python_bindings()

    print("\n=== BRIDGE TEST COMPLETE ===")

    # Save results to file for easier reading
    with open("bridge_test_results.txt", "w") as f:
        f.write("=== AURACRON BRIDGE TEST RESULTS ===\n\n")
        f.write(f"Loaded bridges: {len(loaded_bridges)}\n")
        f.write(f"Failed bridges: {len(failed_bridges)}\n\n")

        if loaded_bridges:
            f.write("✅ LOADED BRIDGES:\n")
            for bridge in loaded_bridges:
                f.write(f"  - {bridge}\n")

        if failed_bridges:
            f.write("\n❌ FAILED BRIDGES:\n")
            for bridge in failed_bridges:
                f.write(f"  - {bridge}\n")

        f.write("\n=== TEST COMPLETE ===\n")

    print("Results saved to bridge_test_results.txt")
