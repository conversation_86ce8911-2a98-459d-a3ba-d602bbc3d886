#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "GameFramework/GameModeBase.h"
#include "Components/ActorComponent.h"
// Ability System não disponível no UE 5.6 - usando alternativas
#include "GameplayTags.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/NetConnection.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundCue.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/DataTable.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "AuracronHarmonyEngineBridge.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogHarmonyEngine, Log, All);

// Forward Declarations
class UHarmonyEngineSubsystem;
class UEmotionalIntelligenceComponent;
class UPositiveBehaviorPredictor;
class UCommunityHealingManager;
class UHarmonyRewardsSystem;
class URealTimeInterventionSystem;

/**
 * Main Bridge class for Auracron Harmony Engine Bridge
 * Primary entry point for Python integration
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONHARMONYENGINEBRIDGE_API UAuracronHarmonyEngineBridge : public UObject
{
    GENERATED_BODY()

public:
    UAuracronHarmonyEngineBridge();
    UAuracronHarmonyEngineBridge(const FObjectInitializer& ObjectInitializer);

    // Static factory method for Python access
    UFUNCTION(BlueprintCallable, Category = "Auracron Harmony Engine")
    static UAuracronHarmonyEngineBridge* GetInstance();

    // Get the main subsystem instance
    UFUNCTION(BlueprintCallable, Category = "Auracron Harmony Engine")
    UHarmonyEngineSubsystem* GetSubsystem(const UObject* WorldContext = nullptr);

private:
    static UAuracronHarmonyEngineBridge* Instance;
};

/**
 * Harmony Engine Bridge Module
 * Revolutionary anti-toxicity system with predictive AI and community healing
 */
class AURACRONHARMONYENGINEBRIDGE_API FAuracronHarmonyEngineBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /** Get the singleton instance of this module */
    static FAuracronHarmonyEngineBridgeModule& Get();

    /** Check if the module is loaded */
    static bool IsAvailable();

private:
    /** Initialize Harmony Engine systems */
    void InitializeHarmonyEngine();
    
    /** Cleanup Harmony Engine systems */
    void CleanupHarmonyEngine();
    
    /** Register Harmony Engine gameplay tags */
    void RegisterHarmonyGameplayTags();

    /** Initialize GameplayTags safely during module startup */
    void InitializeGameplayTags();

    /** Setup ML models for behavioral prediction */
    void InitializeMachineLearningModels();

    /** Module singleton instance */
    static FAuracronHarmonyEngineBridgeModule* ModuleInstance;
};

/**
 * Harmony Engine Core Data Structures
 */

UENUM(BlueprintType)
enum class EHarmonyBehaviorType : uint8
{
    Positive        UMETA(DisplayName = "Positive Behavior"),
    Neutral         UMETA(DisplayName = "Neutral Behavior"),
    Warning         UMETA(DisplayName = "Warning Signs"),
    Toxic           UMETA(DisplayName = "Toxic Behavior"),
    Healing         UMETA(DisplayName = "Healing Behavior")
};

UENUM(BlueprintType)
enum class EEmotionalState : uint8
{
    Happy           UMETA(DisplayName = "Happy"),
    Excited         UMETA(DisplayName = "Excited"),
    Calm            UMETA(DisplayName = "Calm"),
    Neutral         UMETA(DisplayName = "Neutral"),
    Frustrated      UMETA(DisplayName = "Frustrated"),
    Angry           UMETA(DisplayName = "Angry"),
    Sad             UMETA(DisplayName = "Sad"),
    Anxious         UMETA(DisplayName = "Anxious"),
    Bored           UMETA(DisplayName = "Bored"),
    Stressed        UMETA(DisplayName = "Stressed")
};

UENUM(BlueprintType)
enum class EInterventionType : uint8
{
    None            UMETA(DisplayName = "No Intervention"),
    Gentle          UMETA(DisplayName = "Gentle Suggestion"),
    Moderate        UMETA(DisplayName = "Moderate Intervention"),
    Strong          UMETA(DisplayName = "Strong Intervention"),
    Emergency       UMETA(DisplayName = "Emergency Intervention")
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FPlayerBehaviorSnapshot
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EHarmonyBehaviorType BehaviorType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EEmotionalState EmotionalState;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ToxicityScore;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float PositivityScore;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float FrustrationLevel;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 PositiveActionsCount;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 NegativeActionsCount;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SessionDuration;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime Timestamp;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer BehaviorTags;

    FPlayerBehaviorSnapshot()
    {
        PlayerID = TEXT("");
        BehaviorType = EHarmonyBehaviorType::Neutral;
        EmotionalState = EEmotionalState::Neutral;
        ToxicityScore = 0.0f;
        PositivityScore = 0.0f;
        FrustrationLevel = 0.0f;
        PositiveActionsCount = 0;
        NegativeActionsCount = 0;
        SessionDuration = 0.0f;
        Timestamp = FDateTime::Now();
    }
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FHarmonyInterventionData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EInterventionType InterventionType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString InterventionMessage;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString SuggestedAction;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float InterventionPriority;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bRequiresImmediateAction;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer InterventionTags;

    FHarmonyInterventionData()
    {
        InterventionType = EInterventionType::None;
        InterventionMessage = TEXT("");
        SuggestedAction = TEXT("");
        InterventionPriority = 0.0f;
        bRequiresImmediateAction = false;
    }
};

USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FKindnessReward
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 KindnessPoints;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ExperienceMultiplier;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString RewardDescription;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer RewardTags;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsSpecialReward;

    FKindnessReward()
    {
        KindnessPoints = 0;
        ExperienceMultiplier = 1.0f;
        RewardDescription = TEXT("");
        bIsSpecialReward = false;
    }
};

/**
 * Delegate declarations for Harmony Engine events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBehaviorDetected, const FString&, PlayerID, const FPlayerBehaviorSnapshot&, BehaviorData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInterventionTriggered, const FString&, PlayerID, const FHarmonyInterventionData&, InterventionData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCommunityHealing, const FString&, HealerID, const FString&, VictimID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnKindnessReward, const FString&, PlayerID, const FKindnessReward&, Reward);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnHarmonyLevelChanged, int32, NewHarmonyLevel);

/**
 * Extended player behavior data for monitoring and analysis
 */
USTRUCT(BlueprintType)
struct AURACRONHARMONYENGINEBRIDGE_API FPlayerBehaviorData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PlayerID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float PositivityScore;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ToxicityScore;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EEmotionalState EmotionalState;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 KindnessPoints;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 HelpfulActionsCount;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 ToxicActionsCount;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SessionCount;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime MonitoringStartTime;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime LastActivity;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FDateTime LastPositiveAction;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FTimespan TotalPlayTime;

    FPlayerBehaviorData()
    {
        PlayerID = TEXT("");
        PositivityScore = 0.5f;
        ToxicityScore = 0.0f;
        EmotionalState = EEmotionalState::Neutral;
        KindnessPoints = 0;
        HelpfulActionsCount = 0;
        ToxicActionsCount = 0;
        SessionCount = 0;
        MonitoringStartTime = FDateTime::Now();
        LastActivity = FDateTime::Now();
        LastPositiveAction = FDateTime::Now() - FTimespan::FromHours(24);
        TotalPlayTime = FTimespan::Zero();
    }
};

/**
 * Main Harmony Engine Gameplay Tags
 */
namespace HarmonyEngineGameplayTags
{
    // Behavior Detection Tags - initialized in StartupModule
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Behavior_Positive;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Behavior_Toxic;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Behavior_Healing;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Behavior_Mentoring;

    // Emotional State Tags - initialized in StartupModule
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Emotion_Happy;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Emotion_Frustrated;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Emotion_Angry;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Emotion_Calm;

    // Intervention Tags - initialized in StartupModule
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Intervention_Gentle;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Intervention_Moderate;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Intervention_Strong;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Intervention_Emergency;

    // Reward Tags - initialized in StartupModule
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Reward_Kindness;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Reward_Mentorship;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Reward_CommunityHero;
    AURACRONHARMONYENGINEBRIDGE_API extern FGameplayTag Reward_Healing;
}
