#!/usr/bin/env python3
"""
Teste das correções aplicadas aos bridges Auracron
"""

import unreal

def test_corrected_apis():
    """Testa as APIs corrigidas do UE 5.6"""
    print("=== TESTE DAS CORREÇÕES APLICADAS ===")
    
    # Teste 1: DataLayerEditorSubsystem (corrigido)
    try:
        data_layer_subsystem = unreal.get_editor_subsystem(unreal.DataLayerEditorSubsystem)
        if data_layer_subsystem:
            print("✅ DataLayerEditorSubsystem: CORRIGIDO - API funciona")
        else:
            print("❌ DataLayerEditorSubsystem: Não encontrado")
    except Exception as e:
        print(f"❌ DataLayerEditorSubsystem: Erro - {e}")
    
    # Teste 2: WorldPartitionEditorSubsystem (corrigido)
    try:
        wp_subsystem = unreal.get_editor_subsystem(unreal.WorldPartitionEditorSubsystem)
        if wp_subsystem:
            print("✅ WorldPartitionEditorSubsystem: CORRIGIDO - API funciona")
        else:
            print("❌ WorldPartitionEditorSubsystem: Não encontrado")
    except Exception as e:
        print(f"❌ WorldPartitionEditorSubsystem: Erro - {e}")
    
    # Teste 3: Enum DataLayerType (corrigido)
    try:
        layer_type = unreal.DataLayerType.RUNTIME
        print(f"✅ DataLayerType.RUNTIME: CORRIGIDO - {layer_type}")
    except Exception as e:
        print(f"❌ DataLayerType.RUNTIME: Erro - {e}")
    
    # Teste 4: AuracronRealmsBridge como Component (corrigido)
    try:
        realms_bridge_class = getattr(unreal, 'AuracronRealmsBridge', None)
        if realms_bridge_class:
            print("✅ AuracronRealmsBridge: CORRIGIDO - Reconhecido como Component")
        else:
            print("❌ AuracronRealmsBridge: Não encontrado")
    except Exception as e:
        print(f"❌ AuracronRealmsBridge: Erro - {e}")
    
    print("\n=== RESUMO DAS CORREÇÕES ===")
    print("1. ✅ get_world_subsystem → get_editor_subsystem")
    print("2. ✅ get_world_partition → WorldPartitionEditorSubsystem")
    print("3. ✅ layer_type enum → DataLayerType.RUNTIME")
    print("4. ✅ bEnableLandscapeStreaming → set_editor_property")
    print("5. ✅ AuracronRealmsBridge → Component (não Actor)")

if __name__ == "__main__":
    test_corrected_apis()
