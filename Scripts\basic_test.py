#!/usr/bin/env python3

import unreal

unreal.log("=== AURACRON BRIDGES TEST - INICIANDO ===")

try:
    # Testar se os bridges estão disponíveis
    bridges_to_test = [
        'AuracronRealmsBridge',
        'AuracronDataLayerManager',
        'EAuracronDataLayerType',
        'AuracronDynamicRealmSubsystem',
        'AuracronVerticalConnectorManager'
    ]

    unreal.log("=== TESTANDO BRIDGES ===")

    for bridge_name in bridges_to_test:
        try:
            bridge_class = getattr(unreal, bridge_name)
            unreal.log(f"[PASS] {bridge_name}: DISPONÍVEL - {bridge_class}")
        except AttributeError:
            unreal.log_error(f"[FAIL] {bridge_name}: NÃO ENCONTRADO")

    # Testar se conseguimos acessar o subsystem
    unreal.log("=== TESTANDO SUBSYSTEM ===")
    try:
        world = unreal.EditorLevelLibrary.get_editor_world()
        if world:
            unreal.log(f"[PASS] World encontrado: {world}")

            # Tentar obter o subsystem usando APIs corretas do UE 5.6
            try:
                # Primeiro tentar como world subsystem
                subsystem_class = unreal.find_class("AuracronDynamicRealmSubsystem")
                if subsystem_class:
                    subsystem = world.get_subsystem(subsystem_class)
                    if subsystem:
                        unreal.log(f"[PASS] AuracronDynamicRealmSubsystem encontrado (World): {subsystem}")
                    else:
                        # Tentar como editor subsystem
                        subsystem = unreal.get_editor_subsystem(subsystem_class)
                        if subsystem:
                            unreal.log(f"[PASS] AuracronDynamicRealmSubsystem encontrado (Editor): {subsystem}")
                        else:
                            unreal.log_error("[FAIL] AuracronDynamicRealmSubsystem NÃO encontrado")
                else:
                    unreal.log_error("[FAIL] Classe AuracronDynamicRealmSubsystem NÃO encontrada")
            except Exception as e:
                unreal.log_error(f"[FAIL] Erro ao buscar subsystem: {e}")
        else:
            unreal.log_error("[FAIL] World NÃO encontrado")
    except Exception as e:
        unreal.log_error(f"[FAIL] Erro ao acessar subsystem: {e}")

    # Testar curves
    unreal.log("=== TESTANDO CURVES ===")
    curves_to_test = [
        '/Game/Curves/VerticalConnectors/Curve_PortalAnima_Movement',
        '/Game/Curves/VerticalConnectors/Curve_FendaFluxo_Movement',
        '/Game/Curves/VerticalConnectors/Curve_CipoAstria_Movement',
        '/Game/Curves/VerticalConnectors/Curve_ElevadorVortice_Movement',
        '/Game/Curves/VerticalConnectors/Curve_RespiradoroGeotermal_Movement'
    ]

    for curve_path in curves_to_test:
        try:
            curve = unreal.EditorAssetLibrary.load_asset(curve_path)
            if curve:
                unreal.log(f"[PASS] Curve encontrada: {curve_path}")
            else:
                unreal.log_error(f"[FAIL] Curve NÃO encontrada: {curve_path}")
        except Exception as e:
            unreal.log_error(f"[FAIL] Erro ao carregar curve {curve_path}: {e}")

    unreal.log("=== TESTE CONCLUÍDO ===")

except Exception as e:
    unreal.log_error(f"ERRO GERAL: {e}")
    import traceback
    unreal.log_error(f"TRACEBACK: {traceback.format_exc()}")
