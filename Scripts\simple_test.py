#!/usr/bin/env python3
"""
AURACRON - Teste Simples de Python
Script para verificar se o Python está funcionando no UE
"""

import unreal

# Usar unreal.log para garantir que apareça no log
unreal.log("=== TESTE SIMPLES DE PYTHON NO UE ===")
unreal.log("Python está funcionando!")

# Testar acesso básico ao unreal
try:
    unreal.log(f"Módulo unreal carregado: {unreal}")
    unreal.log(f"Versão do UE: {unreal.SystemLibrary.get_engine_version()}")

    # Testar se os bridges estão disponíveis
    bridges_to_test = [
        'AuracronRealmsBridge',
        'AuracronDataLayerManager',
        'EAuracronDataLayerType'
    ]

    for bridge_name in bridges_to_test:
        try:
            bridge_class = getattr(unreal, bridge_name)
            unreal.log(f"[PASS] {bridge_name}: DISPONÍVEL")
        except AttributeError:
            unreal.log(f"[FAIL] {bridge_name}: NÃO ENCONTRADO")

    unreal.log("Teste concluído com sucesso!")

except Exception as e:
    unreal.log_error(f"ERRO: {e}")
