#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "Logging/LogMacros.h"
#include "UObject/ObjectMacros.h"
#include "UObject/Object.h"
#include "Engine/DataTable.h"
#include "Engine/Blueprint.h"
#include "AuracronDynamicRealmBridgeAPI.h"

#include "AuracronDynamicRealmBridge.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronDynamicRealm, Log, All);

/**
 * Auracron Dynamic Realm Bridge Module
 * 
 * Implements the revolutionary three-layer dynamic realm system:
 * - <PERSON><PERSON><PERSON> Radiante (Terrestrial Layer)
 * - Firmamento Zephyr (Celestial Layer) 
 * - Abismo Umbrio (Abyssal Layer)
 * 
 * Features:
 * - Dynamic vertical transitions between layers
 * - Procedural content generation for each realm
 * - Real-time environmental evolution
 * - Cross-layer combat mechanics
 * - Adaptive AI behavior per layer
 * - Performance optimization for multi-layer rendering
 */
class FAuracronDynamicRealmBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;
    
    /** Check if module is available */
    static inline bool IsAvailable()
    {
        return FModuleManager::Get().IsModuleLoaded("AuracronDynamicRealmBridge");
    }
    
    /** Get module instance */
    static inline FAuracronDynamicRealmBridgeModule& Get()
    {
        return FModuleManager::LoadModuleChecked<FAuracronDynamicRealmBridgeModule>("AuracronDynamicRealmBridge");
    }

private:
    void RegisterDynamicRealmSystems();
    void UnregisterDynamicRealmSystems();
    void InitializeRealmManagers();
    void ShutdownRealmManagers();
    void RegisterConsoleCommands();
    void UnregisterConsoleCommands();
    
    // Console command handlers
    void DebugShowRealmInfo();
    void DebugToggleLayerVisibility(int32 LayerIndex);
    void DebugTriggerRealmTransition(int32 SourceLayer, int32 TargetLayer);
    void DebugSpawnRealmContent(int32 LayerIndex);
    void DebugAnalyzeRealmPerformance();
    
    // Module state
    bool bIsInitialized;
    // UE 5.6 Compatible - IConsoleCommand is a struct, not class
    TArray<struct IConsoleCommand*> ConsoleCommands;
};

// Realm layer enumeration
UENUM(BlueprintType)
enum class EAuracronRealmLayer : uint8
{
    None            UMETA(DisplayName = "None"),
    Terrestrial     UMETA(DisplayName = "Planície Radiante (Terrestrial)"),
    Celestial       UMETA(DisplayName = "Firmamento Zephyr (Celestial)"),
    Abyssal         UMETA(DisplayName = "Abismo Umbrio (Abyssal)"),
    Aquatic         UMETA(DisplayName = "Aquatic Layer"),
    Aerial          UMETA(DisplayName = "Aerial Layer"),
    Solar           UMETA(DisplayName = "Solar Layer"),
    Axis            UMETA(DisplayName = "Axis Layer"),
    Lunar           UMETA(DisplayName = "Lunar Layer"),
    Transition      UMETA(DisplayName = "Transition Zone"),
    All             UMETA(DisplayName = "All Layers"),
    MAX             UMETA(Hidden)
};

// Layer evolution stage enumeration
UENUM(BlueprintType)
enum class EAuracronLayerEvolutionStage : uint8
{
    Dormant         UMETA(DisplayName = "Dormant"),
    Awakening       UMETA(DisplayName = "Awakening"),
    Active          UMETA(DisplayName = "Active"),
    Resonant        UMETA(DisplayName = "Resonant"),
    Transcendent    UMETA(DisplayName = "Transcendent")
};

// Global evolution event enumeration
UENUM(BlueprintType)
enum class EAuracronGlobalEvolutionEvent : uint8
{
    AllLayersAwakened   UMETA(DisplayName = "All Layers Awakened"),
    TranscendentReached UMETA(DisplayName = "Transcendent Reached"),
    MaximumEvolution    UMETA(DisplayName = "Maximum Evolution")
};

// Evolution trigger enumeration
UENUM(BlueprintType)
enum class EAuracronEvolutionTrigger : uint8
{
    PlayerPresence      UMETA(DisplayName = "Player Presence"),
    PlayerActivity      UMETA(DisplayName = "Player Activity"),
    CombatActivity      UMETA(DisplayName = "Combat Activity"),
    SigilActivation     UMETA(DisplayName = "Sigil Activation"),
    RailUsage          UMETA(DisplayName = "Rail Usage"),
    IslandActivation   UMETA(DisplayName = "Island Activation"),
    TimeProgression    UMETA(DisplayName = "Time Progression"),
    WeatherEvents      UMETA(DisplayName = "Weather Events"),
    DayNightCycle      UMETA(DisplayName = "Day/Night Cycle"),
    SeasonalChange     UMETA(DisplayName = "Seasonal Change")
};

/**
 * Layer evolution data structure
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronLayerEvolutionData
{
    GENERATED_BODY()

    /** Layer being tracked */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    EAuracronRealmLayer Layer;

    /** Current evolution stage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    EAuracronLayerEvolutionStage EvolutionStage;

    /** Evolution progress (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float EvolutionProgress;

    /** Energy accumulation for evolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float EnergyAccumulation;

    /** Time when current stage started */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float StageStartTime;

    /** Last evolution update time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float LastEvolutionTime;

    /** Evolution speed multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float EvolutionSpeed;

    /** Player influence on evolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float PlayerInfluence;

    /** Environmental factors affecting evolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float EnvironmentalFactors;

    /** Required player time in layer for evolution */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float RequiredPlayerTime;

    /** Whether layer can evolve */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    bool bCanEvolve;

    /** Whether evolution is automatic */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    bool bAutoEvolution;

    /** Layer stability level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Evolution")
    float StabilityLevel;

    FAuracronLayerEvolutionData()
    {
        Layer = EAuracronRealmLayer::Terrestrial;
        EvolutionStage = EAuracronLayerEvolutionStage::Dormant;
        EvolutionProgress = 0.0f;
        StageStartTime = 0.0f;
        LastEvolutionTime = 0.0f;
        EvolutionSpeed = 1.0f;
        StabilityLevel = 1.0f;
        PlayerInfluence = 0.0f;
        EnvironmentalFactors = 1.0f;
        RequiredPlayerTime = 300.0f;
        bCanEvolve = true;
        bAutoEvolution = true;
    }
};

/**
 * Global evolution data structure
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronGlobalEvolutionData
{
    GENERATED_BODY()

    /** Total evolution time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Evolution")
    float TotalEvolutionTime;

    /** Number of layers awakened */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Evolution")
    int32 LayersAwakened;

    /** Number of layers transcendent */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Evolution")
    int32 LayersTranscendent;

    /** Global evolution level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Evolution")
    int32 GlobalEvolutionLevel;

    /** Last global event triggered */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Evolution")
    EAuracronGlobalEvolutionEvent LastGlobalEvent;

    /** Whether maximum evolution has been reached */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global Evolution")
    bool bMaxEvolutionReached;

    FAuracronGlobalEvolutionData()
    {
        TotalEvolutionTime = 0.0f;
        LayersAwakened = 0;
        LayersTranscendent = 0;
        GlobalEvolutionLevel = 0;
        LastGlobalEvent = EAuracronGlobalEvolutionEvent::AllLayersAwakened;
        bMaxEvolutionReached = false;
    }
};

// Realm evolution phases
UENUM(BlueprintType)
enum class ERealmEvolutionPhase : uint8
{
    Despertar       UMETA(DisplayName = "Despertar (0-15min)"),
    Convergencia    UMETA(DisplayName = "Convergência (15-25min)"),
    Intensificacao  UMETA(DisplayName = "Intensificação (25-35min)"),
    Resolucao       UMETA(DisplayName = "Resolução (35+min)")
};

// Realm transition types
UENUM(BlueprintType)
enum class ERealmTransitionType : uint8
{
    Instant         UMETA(DisplayName = "Instant Teleport"),
    Gradual         UMETA(DisplayName = "Gradual Transition"),
    Cinematic       UMETA(DisplayName = "Cinematic Transition"),
    Combat          UMETA(DisplayName = "Combat Transition"),
    Stealth         UMETA(DisplayName = "Stealth Transition")
};

// Realm transition states
UENUM(BlueprintType)
enum class ERealmTransitionState : uint8
{
    Idle            UMETA(DisplayName = "Idle"),
    Preparing       UMETA(DisplayName = "Preparing"),
    FadingOut       UMETA(DisplayName = "Fading Out"),
    Transitioning   UMETA(DisplayName = "Transitioning"),
    FadingIn        UMETA(DisplayName = "Fading In"),
    InProgress      UMETA(DisplayName = "In Progress"),
    Completing      UMETA(DisplayName = "Completing"),
    Paused          UMETA(DisplayName = "Paused"),
    Cancelled       UMETA(DisplayName = "Cancelled"),
    Failed          UMETA(DisplayName = "Failed")
};

// Island types in Fluxo Prismal Serpentino
UENUM(BlueprintType)
enum class EPrismalIslandType : uint8
{
    Nexus           UMETA(DisplayName = "Nexus (Control)"),
    Santuario       UMETA(DisplayName = "Santuário (Safe Zone)"),
    Arsenal         UMETA(DisplayName = "Arsenal (Upgrades)"),
    Caos            UMETA(DisplayName = "Caos (High Risk)")
};

// Rail types for dynamic movement
UENUM(BlueprintType)
enum class EAuracronRailType : uint8
{
    Solar           UMETA(DisplayName = "Solar Trilhos (Golden Speed)"),
    Axis            UMETA(DisplayName = "Axis Trilhos (Vertical Instant)"),
    Lunar           UMETA(DisplayName = "Lunar Trilhos (Ethereal Stealth)")
};

// Sigilo types for dynamic realm abilities
UENUM(BlueprintType)
enum class EAuracronDynamicRealmSigiloType : uint8
{
    Aegis           UMETA(DisplayName = "Aegis (Defense)"),
    Ruin            UMETA(DisplayName = "Ruin (Attack)"),
    Vesper          UMETA(DisplayName = "Vesper (Stealth)"),
    Fusion          UMETA(DisplayName = "Fusion (Utility)")
};

/**
 * Estrutura para dados de movimento do jogador no trilho
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FPlayerRailData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Rail Movement")
    TWeakObjectPtr<APawn> Player;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Movement")
    float Progress = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Movement")
    bool bMovingForward = true;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Movement")
    float CurrentSpeed = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Movement")
    float StartTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Movement")
    bool bIsActive = false;

    FPlayerRailData()
    {
        Progress = 0.0f;
        bMovingForward = true;
        CurrentSpeed = 0.0f;
        StartTime = 0.0f;
        bIsActive = false;
    }
};

/**
 * Estrutura para bonus de sígilo por realm
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FSigilRealmBonus
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Realm")
    EAuracronRealmLayer RealmLayer = EAuracronRealmLayer::None;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Realm")
    float AegisBonusMultiplier = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Realm")
    float RuinBonusMultiplier = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Realm")
    float VesperBonusMultiplier = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Realm")
    float FusionBonusMultiplier = 1.0f;

    FSigilRealmBonus()
    {
        RealmLayer = EAuracronRealmLayer::None;
        AegisBonusMultiplier = 1.0f;
        RuinBonusMultiplier = 1.0f;
        VesperBonusMultiplier = 1.0f;
        FusionBonusMultiplier = 1.0f;
    }
};

/**
 * Estrutura para configuração de geração de trilhos
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FRailGenerationConfig
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Rail Generation")
    int32 MaxRailsPerLayer = 8;

    UPROPERTY(BlueprintReadWrite, Category = "Rail Generation")
    float MinRailLength = 1000.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Rail Generation")
    float MaxRailLength = 5000.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Rail Generation")
    float RailSpacing = 800.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Rail Generation")
    bool bAutoGenerateRails = true;

    UPROPERTY(BlueprintReadWrite, Category = "Rail Generation")
    bool bAdaptiveGeneration = true;

    FRailGenerationConfig()
    {
        MaxRailsPerLayer = 8;
        MinRailLength = 1000.0f;
        MaxRailLength = 5000.0f;
        RailSpacing = 800.0f;
        bAutoGenerateRails = true;
        bAdaptiveGeneration = true;
    }
};

// Forward declarations
class AAuracronDynamicRail;

/**
 * Estrutura para métricas do sistema de trilhos
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FRailSystemMetrics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Rail Metrics")
    TWeakObjectPtr<AAuracronDynamicRail> Rail;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Metrics")
    float TotalUsageTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Metrics")
    int32 PlayerCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Metrics")
    float EfficiencyRating = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Metrics")
    float LastUsageTime = 0.0f;

    FRailSystemMetrics()
    {
        TotalUsageTime = 0.0f;
        PlayerCount = 0;
        EfficiencyRating = 1.0f;
        LastUsageTime = 0.0f;
    }
};

/**
 * Estrutura para experiência do jogador com trilhos
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FPlayerRailExperience
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Rail Experience")
    TWeakObjectPtr<APawn> Player;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Experience")
    float TotalRailTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Experience")
    int32 RailsUsed = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Experience")
    int32 ExperienceLevel = 1;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Experience")
    float ExperiencePoints = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Experience")
    float SpeedMultiplier = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Rail Experience")
    float EnergyEfficiency = 1.0f;

    FPlayerRailExperience()
    {
        TotalRailTime = 0.0f;
        RailsUsed = 0;
        ExperienceLevel = 1;
        ExperiencePoints = 0.0f;
        SpeedMultiplier = 1.0f;
        EnergyEfficiency = 1.0f;
    }
};

// === Delegates for System Events ===

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnSystemFullyInitialized);

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class AAuracronRealmManager;
class UAuracronLayerComponent;
class AAuracronPrismalFlow;
class AAuracronDynamicRail;
class UAuracronRealmTransitionComponent;

/**
 * Main Bridge class for Auracron Dynamic Realm Bridge
 * Primary entry point for Python integration
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronDynamicRealmBridge : public UObject
{
    GENERATED_BODY()

public:
    UAuracronDynamicRealmBridge();
    UAuracronDynamicRealmBridge(const FObjectInitializer& ObjectInitializer);

    // Static factory method for Python access
    UFUNCTION(BlueprintCallable, Category = "Auracron Dynamic Realm")
    static UAuracronDynamicRealmBridge* GetInstance();

    // Get the main subsystem instance
    UFUNCTION(BlueprintCallable, Category = "Auracron Dynamic Realm")
    UAuracronDynamicRealmSubsystem* GetSubsystem(const UObject* WorldContext = nullptr);

private:
    static UAuracronDynamicRealmBridge* Instance;
};

// Delegate declarations
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRealmLayerChanged, EAuracronRealmLayer, OldLayer, EAuracronRealmLayer, NewLayer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRealmEvolutionPhaseChanged, ERealmEvolutionPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnRealmTransitionStarted, AActor*, Actor, EAuracronRealmLayer, SourceLayer, EAuracronRealmLayer, TargetLayer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnRealmTransitionCompleted, AActor*, Actor, EAuracronRealmLayer, SourceLayer, EAuracronRealmLayer, TargetLayer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPrismalIslandActivated, EPrismalIslandType, IslandType, FVector, Location);

// Advanced evolution system delegates
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnLayerEvolutionStageChanged, EAuracronRealmLayer, Layer, EAuracronLayerEvolutionStage, OldStage, EAuracronLayerEvolutionStage, NewStage);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FOnAdvancedRealmTransitionComplete, AActor*, Actor, EAuracronRealmLayer, SourceLayer, EAuracronRealmLayer, TargetLayer, float, Duration);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGlobalEvolutionEvent, EAuracronGlobalEvolutionEvent, EventType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnTranscendentContentUnlocked);

// Utility macros for Dynamic Realm system
#define AURACRON_REALM_LOG(Verbosity, Format, ...) \
    UE_LOG(LogAuracronDynamicRealm, Verbosity, Format, ##__VA_ARGS__)

#define AURACRON_REALM_CLOG(Condition, Verbosity, Format, ...) \
    UE_CLOG(Condition, LogAuracronDynamicRealm, Verbosity, Format, ##__VA_ARGS__)

// Performance profiling macros
#if !UE_BUILD_SHIPPING
    #define AURACRON_REALM_SCOPE_CYCLE_COUNTER(StatId) SCOPE_CYCLE_COUNTER(StatId)
    #define AURACRON_REALM_DECLARE_CYCLE_STAT(StatId, StatName, GroupId) DECLARE_CYCLE_STAT(StatName, StatId, GroupId)
#else
    #define AURACRON_REALM_SCOPE_CYCLE_COUNTER(StatId)
    #define AURACRON_REALM_DECLARE_CYCLE_STAT(StatId, StatName, GroupId)
#endif

// Performance stats
AURACRON_REALM_DECLARE_CYCLE_STAT(STAT_AuracronRealmUpdate, TEXT("Auracron Realm Update"), STATGROUP_Game);
AURACRON_REALM_DECLARE_CYCLE_STAT(STAT_AuracronRealmTransition, TEXT("Auracron Realm Transition"), STATGROUP_Game);
AURACRON_REALM_DECLARE_CYCLE_STAT(STAT_AuracronRealmGeneration, TEXT("Auracron Realm Generation"), STATGROUP_Game);
AURACRON_REALM_DECLARE_CYCLE_STAT(STAT_AuracronRealmRendering, TEXT("Auracron Realm Rendering"), STATGROUP_Game);

// Version information
#define AURACRON_DYNAMIC_REALM_VERSION_MAJOR 1
#define AURACRON_DYNAMIC_REALM_VERSION_MINOR 0
#define AURACRON_DYNAMIC_REALM_VERSION_PATCH 0
#define AURACRON_DYNAMIC_REALM_VERSION_STRING "1.0.0"

// Feature availability checks
#define AURACRON_DYNAMIC_REALM_AVAILABLE() (FAuracronDynamicRealmBridgeModule::IsAvailable())
#define AURACRON_DYNAMIC_REALM_ENABLED() (WITH_AURACRON_DYNAMIC_REALM && AURACRON_DYNAMIC_REALM_AVAILABLE())

// Configuration constants
namespace AuracronDynamicRealmConstants
{
    // Layer heights (in Unreal units)
    constexpr float TERRESTRIAL_LAYER_HEIGHT = 0.0f;
    constexpr float CELESTIAL_LAYER_HEIGHT = 5000.0f;
    constexpr float ABYSSAL_LAYER_HEIGHT = -3000.0f;
    
    // Transition parameters
    constexpr float DEFAULT_TRANSITION_DURATION = 2.0f;
    constexpr float INSTANT_TRANSITION_DURATION = 0.1f;
    constexpr float CINEMATIC_TRANSITION_DURATION = 5.0f;
    
    // Performance limits
    constexpr int32 MAX_CONCURRENT_TRANSITIONS = 10;
    constexpr int32 MAX_ACTIVE_ISLANDS = 50;
    constexpr int32 MAX_DYNAMIC_RAILS = 20;
    
    // Evolution phase durations (in seconds)
    constexpr float DESPERTAR_DURATION = 900.0f;      // 15 minutes
    constexpr float CONVERGENCIA_DURATION = 600.0f;   // 10 minutes
    constexpr float INTENSIFICACAO_DURATION = 600.0f; // 10 minutes
    // Resolução has no time limit
    
    // Prismal Flow parameters
    constexpr float PRISMAL_FLOW_WIDTH = 1000.0f;
    constexpr float PRISMAL_FLOW_SPEED = 500.0f;
    constexpr int32 TOTAL_NEXUS_ISLANDS = 5;
    constexpr int32 TOTAL_SANTUARIO_ISLANDS = 8;
    constexpr int32 TOTAL_ARSENAL_ISLANDS = 6;
    constexpr int32 TOTAL_CAOS_ISLANDS = 4;
}

// Global access functions
namespace AuracronDynamicRealmUtils
{
    /** Get the Dynamic Realm Subsystem */
    AURACRONDYNAMICREALMBRIDGE_API UAuracronDynamicRealmSubsystem* GetDynamicRealmSubsystem(const UObject* WorldContext);

    /** Convert layer enum to height */
    AURACRONDYNAMICREALMBRIDGE_API float GetLayerHeight(EAuracronRealmLayer Layer);

    /** Get layer from world position */
    AURACRONDYNAMICREALMBRIDGE_API EAuracronRealmLayer GetLayerFromPosition(const FVector& WorldPosition);

    /** Check if position is in transition zone */
    AURACRONDYNAMICREALMBRIDGE_API bool IsInTransitionZone(const FVector& WorldPosition);

    /** Calculate transition duration based on type and distance */
    AURACRONDYNAMICREALMBRIDGE_API float CalculateTransitionDuration(ERealmTransitionType TransitionType, float Distance);
}
