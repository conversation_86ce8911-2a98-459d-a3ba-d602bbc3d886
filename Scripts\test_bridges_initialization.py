#!/usr/bin/env python3
"""
AURACRON - Teste de Inicialização dos Bridges
Script para testar se todos os bridges Auracron estão sendo inicializados corretamente no Python
"""

import unreal

def test_bridge_availability():
    """Testa se os bridges estão disponíveis no módulo unreal"""
    print("=== TESTE DE DISPONIBILIDADE DOS BRIDGES ===")
    
    bridges_to_test = [
        'AuracronRealmsBridge',
        'AuracronWorldPartitionPythonBridge', 
        'AuracronDataLayerManager',
        'AuracronWorldPartitionLandscapeManager',
        'EAuracronDataLayerType',
        'EAuracronRealmType'
    ]
    
    available_bridges = []
    missing_bridges = []
    
    for bridge_name in bridges_to_test:
        try:
            bridge_class = getattr(unreal, bridge_name)
            available_bridges.append(bridge_name)
            print(f"[PASS] {bridge_name}: {bridge_class}")
        except AttributeError:
            missing_bridges.append(bridge_name)
            print(f"[FAIL] {bridge_name}: Não encontrado no módulo unreal")
    
    print(f"\n[RESULTADO] {len(available_bridges)}/{len(bridges_to_test)} bridges disponíveis")
    print(f"[DISPONÍVEIS] {available_bridges}")
    print(f"[AUSENTES] {missing_bridges}")
    
    return available_bridges, missing_bridges

def test_bridge_instantiation():
    """Testa se os bridges podem ser instanciados"""
    print("\n=== TESTE DE INSTANCIAÇÃO DOS BRIDGES ===")
    
    # Teste 1: AuracronRealmsBridge (Component)
    try:
        # UE 5.6: AuracronRealmsBridge é um GameFrameworkComponent, não um Actor
        realms_bridge_class = getattr(unreal, 'AuracronRealmsBridge', None)
        if realms_bridge_class:
            print(f"[INFO] AuracronRealmsBridge encontrado: {realms_bridge_class}")
            print(f"[INFO] Tipo: {type(realms_bridge_class)}")
            # Verificar se é um Component (não Actor)
            if hasattr(realms_bridge_class, '__bases__'):
                print(f"[INFO] Herança: {realms_bridge_class.__bases__}")
            print("[PASS] AuracronRealmsBridge é um Component, não um Actor")
        else:
            print("[FAIL] AuracronRealmsBridge não encontrado")
    except Exception as e:
        print(f"[ERROR] Erro ao testar AuracronRealmsBridge: {e}")
    
    # Teste 2: AuracronDataLayerManager (Singleton)
    try:
        data_layer_manager_class = getattr(unreal, 'AuracronDataLayerManager', None)
        if data_layer_manager_class:
            print(f"[INFO] AuracronDataLayerManager encontrado: {data_layer_manager_class}")
            # Tentar usar GetInstance
            try:
                instance = data_layer_manager_class.get_instance()
                print(f"[PASS] AuracronDataLayerManager.get_instance(): {instance}")
            except AttributeError:
                try:
                    instance = data_layer_manager_class.GetInstance()
                    print(f"[PASS] AuracronDataLayerManager.GetInstance(): {instance}")
                except AttributeError:
                    print("[FAIL] Método GetInstance não encontrado")
            except Exception as e:
                print(f"[ERROR] Erro ao chamar GetInstance: {e}")
        else:
            print("[FAIL] AuracronDataLayerManager não encontrado")
    except Exception as e:
        print(f"[ERROR] Erro ao testar AuracronDataLayerManager: {e}")
    
    # Teste 3: Enums
    try:
        enum_class = getattr(unreal, 'EAuracronDataLayerType', None)
        if enum_class:
            print(f"[INFO] EAuracronDataLayerType encontrado: {enum_class}")
            # Tentar acessar valores do enum
            try:
                runtime_value = enum_class.Runtime
                print(f"[PASS] EAuracronDataLayerType.Runtime: {runtime_value}")
            except AttributeError:
                try:
                    runtime_value = enum_class.RUNTIME
                    print(f"[PASS] EAuracronDataLayerType.RUNTIME: {runtime_value}")
                except AttributeError:
                    print("[FAIL] Valores do enum não encontrados")
        else:
            print("[FAIL] EAuracronDataLayerType não encontrado")
    except Exception as e:
        print(f"[ERROR] Erro ao testar EAuracronDataLayerType: {e}")

def test_world_access():
    """Testa acesso ao mundo e subsistemas"""
    print("\n=== TESTE DE ACESSO AO MUNDO ===")
    
    try:
        # Teste get_editor_world
        editor_subsystem = unreal.get_editor_subsystem(unreal.UnrealEditorSubsystem)
        editor_world = editor_subsystem.get_editor_world()
        print(f"[PASS] Editor World: {editor_world}")
        
        # Teste get_world_subsystem (se existir)
        try:
            world_subsystem = unreal.get_world_subsystem
            print(f"[PASS] get_world_subsystem disponível: {world_subsystem}")
        except AttributeError:
            print("[FAIL] get_world_subsystem não disponível")
        
        # Teste get_world_partition
        if editor_world:
            try:
                world_partition = editor_world.get_world_partition()
                print(f"[PASS] World Partition: {world_partition}")
            except AttributeError:
                print("[FAIL] get_world_partition não disponível")
        
    except Exception as e:
        print(f"[ERROR] Erro ao testar acesso ao mundo: {e}")

def test_landscape_apis():
    """Testa APIs de Landscape"""
    print("\n=== TESTE DE APIs DE LANDSCAPE ===")
    
    landscape_classes = [
        'Landscape',
        'LandscapeProxy', 
        'LandscapePlaceholder',
        'LandscapeStreamingProxy'
    ]
    
    for class_name in landscape_classes:
        try:
            landscape_class = getattr(unreal, class_name)
            print(f"[PASS] {class_name}: {landscape_class}")
        except AttributeError:
            print(f"[FAIL] {class_name}: Não encontrado")

def main():
    """Função principal do teste"""
    print("AURACRON - Teste de Inicialização dos Bridges")
    print("=" * 60)
    
    # Executar todos os testes
    available_bridges, missing_bridges = test_bridge_availability()
    test_bridge_instantiation()
    test_world_access()
    test_landscape_apis()
    
    # Resultado final
    print("\n" + "=" * 60)
    print("RESULTADO FINAL DO TESTE")
    print("=" * 60)
    
    if len(missing_bridges) == 0:
        print("[SUCCESS] Todos os bridges estão disponíveis!")
    else:
        print(f"[WARNING] {len(missing_bridges)} bridges ausentes: {missing_bridges}")
    
    print("Teste concluído.")

if __name__ == "__main__":
    main()
