#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronRealmsBridge, Log, All);

/**
 * Módulo do Sistema de Realms Dinâmicos do Auracron
 * 
 * Este módulo gerencia o sistema de 3 realms dinâmicos:
 * - <PERSON><PERSON>cie <PERSON> (Surface)
 * - Firmamento Zephyr (Sky) 
 * - Abismo Umbrio (Underground)
 * 
 * Funcionalidades principais:
 * - Transições dinâmicas entre realms
 * - Gerenciamento de Data Layers
 * - Integração com World Partition
 * - Sistema de geração procedural por realm
 * - Exposição de APIs para Python
 */
class AURACRONREALMSBRIDGE_API FAuracronRealmsBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;
    
    /** Verifica se o módulo está carregado */
    static bool IsModuleLoaded();
    
    /** Obtém referência ao módulo */
    static FAuracronRealmsBridgeModule& Get();
    
    /** Verifica se o módulo está inicializado */
    bool IsInitialized() const { return bModuleInitialized; }
    
private:
    /** Flag para indicar se o módulo foi inicializado */
    bool bModuleInitialized = false;
    
    /** Inicializar sistemas de realms */
    void InitializeRealmSystems();
    
    /** Registrar integrações com outros módulos */
    void RegisterModuleIntegrations();
    
    /** Limpar sistemas */
    void CleanupRealmSystems();
};
